<?php
require_once '../config/database.php';
require_once '../auth_check.php';

header('Content-Type: application/json');

if (!isset($_GET['solicitante'])) {
    echo json_encode(['success' => false, 'message' => 'Solicitante não fornecido']);
    exit;
}

$solicitante = $_GET['solicitante'];

// Buscar dados do usuário na API
$apiFields = [
    'api_user' => 'UFL7GXZ14LU9NOR',
    'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
    'api_module' => 'Usuarios',
    'api_action' => 'listarUsuarios'
];

$curl = curl_init();
curl_setopt_array($curl, array(
    CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => http_build_query($apiFields),
));

$response = curl_exec($curl);

if (curl_errno($curl)) {
    echo json_encode(['success' => false, 'message' => 'Erro na API: ' . curl_error($curl)]);
    curl_close($curl);
    exit;
}

curl_close($curl);

$usuarios = json_decode($response, true);

if (!is_array($usuarios)) {
    echo json_encode(['success' => false, 'message' => 'Resposta da API inválida']);
    exit;
}

$dados_usuario = null;
foreach ($usuarios as $user) {
    if (isset($user['status']) && $user['status'] == 1 && 
        isset($user['bloqueado']) && $user['bloqueado'] == 0 && 
        (
            (isset($user['loginAD']) && strtolower($user['loginAD']) === strtolower($solicitante)) ||
            (isset($user['loginSISBR']) && strtolower($user['loginSISBR']) === strtolower($solicitante))
        )) {
        
        $dados_usuario = [
            'nome' => $user['nome_completo'] ?? $user['nome'],
            'foto_url' => !empty($user['foto']) ? 'https://intranet.sicoobcredilivre.com.br/sys/conteudo/usuarios/' . $user['foto'] : null,
            'pa' => $user['pa'] ?? 'N/A',
            'pa_nome' => $user['pa_nome'] ?? 'N/A'
        ];
        break;
    }
}

if ($dados_usuario) {
    echo json_encode(['success' => true, 'data' => $dados_usuario]);
} else {
    echo json_encode(['success' => false, 'message' => 'Usuário não encontrado']);
} 
<?php
// Suprimir todos os erros PHP para garantir apenas saída JSON
error_reporting(0);
ini_set('display_errors', 0);

// Garantir que nenhuma saída seja enviada antes do JSON
ob_start();

header('Content-Type: application/json');

try {
    // Verificar se a biblioteca GD está instalada
    if (!extension_loaded('gd')) {
        throw new Exception('A biblioteca GD não está instalada no servidor.');
    }

    require_once '../auth_check.php';
    require_once '../config/database.php';

    // Verificar se o usuário é administrador ou forte gestor
    $stmt_nivel = $pdo->prepare("SELECT nivel_acesso_id FROM usuarios WHERE id = ?");
    $stmt_nivel->execute([$_SESSION['user_id']]);
    $nivel_usuario = $stmt_nivel->fetchColumn();

    // Verificar se é admin (1) ou forte gestor (2)
    $is_admin = ($nivel_usuario == 1 || $nivel_usuario == 2);

    // Redirecionar se não for admin ou forte gestor
    if (!$is_admin) {
        ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Acesso não autorizado']);
        exit;
    }

    // Verificar se os dados necessários foram enviados
    if (!isset($_POST['emailTo']) || !isset($_POST['percentualCorte']) || !isset($_POST['solicitantes'])) {
        ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
        exit;
    }

    $emailTo = filter_var($_POST['emailTo'], FILTER_VALIDATE_EMAIL);
    if (!$emailTo) {
        ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Email inválido']);
        exit;
    }

    $percentualCorte = floatval($_POST['percentualCorte']);
    $mensagem = isset($_POST['mensagem']) ? $_POST['mensagem'] : '';
    $solicitantes = json_decode($_POST['solicitantes'], true);

    if (!is_array($solicitantes) || empty($solicitantes)) {
        ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Nenhum solicitante selecionado']);
        exit;
    }

    // Buscar dados dos solicitantes
    $placeholders = str_repeat('?,', count($solicitantes) - 1) . '?';
    $query = "SELECT 
        solicitante,
        COUNT(*) as total_registros,
        ROUND((SUM(CASE WHEN status = 'Devolvido' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 1) as percentual_devolucao
    FROM atualizacao_cadastral 
    WHERE solicitante IN ($placeholders)
    AND MONTH(data_cadastro) = MONTH(CURRENT_DATE())
    AND YEAR(data_cadastro) = YEAR(CURRENT_DATE())
    GROUP BY solicitante";

    $stmt = $pdo->prepare($query);
    $stmt->execute($solicitantes);
    $dados = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($dados)) {
        ob_end_clean();
        echo json_encode(['success' => false, 'message' => 'Nenhum dado encontrado para os solicitantes selecionados']);
        exit;
    }

    // Criar imagem
    $width = 800;
    $height = 150 + (count($dados) * 100); // Altura dinâmica baseada no número de solicitantes
    $image = imagecreatetruecolor($width, $height);

    // Cores
    $white = imagecolorallocate($image, 255, 255, 255);
    $green = imagecolorallocate($image, 0, 107, 63); // Verde Sicoob
    $gray = imagecolorallocate($image, 102, 102, 102);
    $lightGray = imagecolorallocate($image, 245, 245, 245);

    // Preencher fundo
    imagefill($image, 0, 0, $white);

    // Adicionar título
    $title = "Relatório de Destaques - Atualização Cadastral";
    $font = realpath(__DIR__ . '/../assets/fonts/arial.ttf');
    imagettftext($image, 20, 0, 30, 50, $green, $font, $title);

    // Adicionar data
    $date = date('d/m/Y');
    imagettftext($image, 12, 0, 30, 80, $gray, $font, "Data: " . $date);

    // Adicionar solicitantes
    $y = 120;
    foreach ($dados as $dado) {
        // Desenhar retângulo de fundo
        imagefilledrectangle($image, 30, $y, $width - 30, $y + 80, $lightGray);
        
        // Nome do solicitante
        imagettftext($image, 16, 0, 50, $y + 30, $green, $font, $dado['solicitante']);
        
        // Taxa de devolução
        $taxa = "Taxa de Devolução: " . $dado['percentual_devolucao'] . "%";
        imagettftext($image, 14, 0, 50, $y + 55, $gray, $font, $taxa);
        
        // Total de registros
        $total = "Total de Registros: " . $dado['total_registros'];
        imagettftext($image, 14, 0, 300, $y + 55, $gray, $font, $total);
        
        $y += 100;
    }

    // Salvar imagem
    $imageFileName = 'relatorio_' . date('YmdHis') . '.png';
    $imagePath = __DIR__ . '/../temp/' . $imageFileName;
    
    // Criar diretório temp se não existir
    if (!file_exists(__DIR__ . '/../temp')) {
        mkdir(__DIR__ . '/../temp', 0777, true);
    }
    
    imagepng($image, $imagePath);
    imagedestroy($image);

    // Construir o corpo do email
    $assunto = "Relatório de Destaques - Atualização Cadastral";
    $corpo = "Relatório de Destaques - Atualização Cadastral\n\n";

    if (!empty($mensagem)) {
        $corpo .= $mensagem . "\n\n";
    }

    $corpo .= "Solicitantes em Destaque:\n\n";

    foreach ($dados as $dado) {
        $corpo .= "- " . $dado['solicitante'] . "\n";
        $corpo .= "  Taxa de Devolução: " . $dado['percentual_devolucao'] . "%\n";
        $corpo .= "  Total de Registros: " . $dado['total_registros'] . "\n\n";
    }

    $corpo .= "Segue em anexo o relatório dos solicitantes em destaque.";

    ob_end_clean();
    echo json_encode([
        'success' => true,
        'emailTo' => $emailTo,
        'assunto' => $assunto,
        'corpo' => $corpo,
        'anexo' => $imagePath
    ]);

} catch (PDOException $e) {
    ob_end_clean();
    error_log('Erro de banco de dados: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao acessar o banco de dados'
    ]);
} catch (Exception $e) {
    ob_end_clean();
    error_log('Erro: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 
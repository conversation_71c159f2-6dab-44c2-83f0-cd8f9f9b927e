/* Estilos para a tabela de ranking */
.ranking-table {
    border-collapse: separate;
    border-spacing: 0;
}

/* <PERSON><PERSON><PERSON><PERSON><PERSON> da tabela */
.ranking-table thead th {
    color: #fff;
    font-weight: 500;
    border: none;
    padding: 12px 8px;
}

/* Alternância de cores para as linhas da tabela */
.ranking-table tbody tr.bg-white {
    background-color: #ffffff !important;
}

.ranking-table tbody tr.bg-light {
    background-color: rgba(0, 174, 157, 0.05) !important;
}

/* Remover background padrão do Bootstrap */
.ranking-table tbody tr {
    background-image: none !important;
}

/* Garantir que as classes bg-white e bg-light se sobreponham */
.table > tbody > tr.bg-white {
    background-color: #ffffff !important;
}

.table > tbody > tr.bg-light {
    background-color: rgba(0, 174, 157, 0.05) !important;
}

/* Efeito hover */
.ranking-table tbody tr:hover,
.ranking-table tbody tr:hover + tr {
    background-color: rgba(0, 174, 157, 0.1) !important;
    transition: background-color 0.2s ease;
}

/* Estilos para os indicadores de bimestre */
.bimester-indicator {
    display: inline-flex;
    align-items: center;
    margin-right: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    background-color: rgba(0, 0, 0, 0.05);
}

.bimester-indicator i {
    font-size: 10px;
    margin-right: 4px;
}

.bimester-indicator small {
    font-size: 11px;
    font-weight: 500;
}

/* Cores dos indicadores de bimestre */
.bimester-indicator i.text-success {
    color: #28a745 !important;
}

.bimester-indicator i.text-warning {
    color: #ffc107 !important;
}

.bimester-indicator i.text-danger {
    color: #dc3545 !important;
}

.bimester-indicator i.text-secondary {
    color: #6c757d !important;
}

.bimester-indicator i.text-black-50 {
    color: #49479D !important; /* Cor roxa da identidade visual */
}

/* Linha de informações do usuário */
.user-info-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
}

.user-name-section {
    flex: 0 0 auto;
    margin-right: 20px;
}

.bimester-section {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

/* Responsividade */
@media (max-width: 768px) {
    .ranking-table {
        font-size: 0.9rem;
    }
    
    .bimester-indicator {
        padding: 2px 6px;
        margin-right: 8px;
    }
    
    .user-info-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .user-name-section {
        margin-bottom: 8px;
    }
}

/* Linha de totais */
.ranking-table tr.table-primary {
    background-color: rgba(0, 174, 157, 0.15) !important;
    font-weight: 600;
}

/* Tooltips personalizados */
.tooltip-inner {
    background-color: rgba(0, 0, 0, 0.9);
    padding: 8px 12px;
    font-size: 12px;
} 
<?php
class TwoFactorAuth {
    private $secretLength = 16;
    
    public function generateSecret() {
        $validChars = array_merge(range('A', 'Z'), range('2', '7'));
        $secret = '';
        for ($i = 0; $i < $this->secretLength; $i++) {
            $secret .= $validChars[array_rand($validChars)];
        }
        return $secret;
    }
    
    public function getQRCodeUrl($name, $secret) {
        $company = urlencode('Sicoob');
        $name = urlencode($name);
        $secret = urlencode($secret);
        // Usando a API do QR Server
        return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=otpauth://totp/{$company}:{$name}?secret={$secret}%26issuer={$company}";
    }
    
    public function verifyCode($secret, $code) {
        $timeSlice = floor(time() / 30);
        
        // Verificar código atual e códigos adjacentes (30 segundos antes e depois)
        for ($i = -1; $i <= 1; $i++) {
            if ($this->generateCode($secret, $timeSlice + $i) == $code) {
                return true;
            }
        }
        return false;
    }
    
    private function generateCode($secret, $timeSlice) {
        $secretkey = $this->base32Decode($secret);
        
        // Pack time into binary string
        $time = pack('N*', 0) . pack('N*', $timeSlice);
        
        // Hash it with users secret key
        $hm = hash_hmac('SHA1', $time, $secretkey, true);
        
        // Use last nipple of result as index/offset
        $offset = ord(substr($hm, -1)) & 0x0F;
        
        // grab 4 bytes of the result
        $hashpart = substr($hm, $offset, 4);
        
        // Unpak binary value
        $value = unpack('N', $hashpart);
        $value = $value[1];
        
        // Only 32 bits
        $value = $value & 0x7FFFFFFF;
        
        return str_pad($value % 1000000, 6, '0', STR_PAD_LEFT);
    }
    
    private function base32Decode($secret) {
        $base32chars = array(
            'A' => 0, 'B' => 1, 'C' => 2, 'D' => 3, 'E' => 4, 'F' => 5, 'G' => 6, 'H' => 7,
            'I' => 8, 'J' => 9, 'K' => 10, 'L' => 11, 'M' => 12, 'N' => 13, 'O' => 14, 'P' => 15,
            'Q' => 16, 'R' => 17, 'S' => 18, 'T' => 19, 'U' => 20, 'V' => 21, 'W' => 22, 'X' => 23,
            'Y' => 24, 'Z' => 25, '2' => 26, '3' => 27, '4' => 28, '5' => 29, '6' => 30, '7' => 31
        );
        
        $secret = strtoupper($secret);
        $binary = '';
        $secretLength = strlen($secret);
        $bitsLeft = 0;
        $currentByte = 0;
        
        for ($i = 0; $i < $secretLength; $i++) {
            $currentByte = ($currentByte << 5) | $base32chars[$secret[$i]];
            $bitsLeft += 5;
            if ($bitsLeft >= 8) {
                $binary .= chr($currentByte >> ($bitsLeft - 8));
                $bitsLeft -= 8;
            }
        }
        
        return $binary;
    }
} 
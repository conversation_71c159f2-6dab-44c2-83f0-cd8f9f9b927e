<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $acao = isset($_POST['acao']) ? $_POST['acao'] : '';
    
    try {
        switch ($acao) {
            case 'quitar':
                $processo_id = intval($_POST['processo_id']);
                
                // Verificar status atual e informações de ajuizamento
                $stmt = $pdo->prepare("SELECT status_id, data_ajuizamento, valor_ajuizado FROM cbp_processos_judiciais WHERE id = ?");
                $stmt->execute([$processo_id]);
                $processo = $stmt->fetch();
                
                if (!$processo) {
                    throw new Exception('Processo não encontrado');
                }
                
                // Verificar se tem informações de ajuizamento
                if ($processo['data_ajuizamento'] === null || $processo['valor_ajuizado'] === null) {
                    throw new Exception('Não é possível quitar um processo sem informações de ajuizamento');
                }
                
                // Se estiver quitado (3), voltar para vigente (1), senão quitar
                $novo_status = $processo['status_id'] == 3 ? 1 : 3;
                
                $stmt = $pdo->prepare("UPDATE cbp_processos_judiciais SET status_id = ? WHERE id = ?");
                $stmt->execute([$novo_status, $processo_id]);
                
                echo json_encode([
                    'success' => true,
                    'message' => $novo_status == 3 ? 'Processo quitado com sucesso!' : 'Processo reaberto com sucesso!'
                ]);
                break;

            case 'verificar_acordo':
                $processo_id = intval($_POST['processo_id']);
                
                // Verificar se o processo tem informações de ajuizamento
                $stmt = $pdo->prepare("SELECT data_ajuizamento, valor_ajuizado FROM cbp_processos_judiciais WHERE id = ?");
                $stmt->execute([$processo_id]);
                $processo = $stmt->fetch();
                
                if (!$processo) {
                    throw new Exception('Processo não encontrado');
                }
                
                if ($processo['data_ajuizamento'] === null || $processo['valor_ajuizado'] === null) {
                    throw new Exception('Não é possível incluir acordos em processos sem informações de ajuizamento');
                }
                
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as total 
                    FROM cbp_acordos 
                    WHERE processo_id = ? AND ativo = 1
                ");
                $stmt->execute([$processo_id]);
                $result = $stmt->fetch();
                
                echo json_encode([
                    'success' => true,
                    'tem_acordo' => $result['total'] > 0
                ]);
                break;

            case 'incluir_acordo':
                $processo_id = intval($_POST['processo_id']);
                $inativar_anteriores = isset($_POST['inativar_anteriores']) && $_POST['inativar_anteriores'] === 'true';
                
                // Verificar se tem informações de ajuizamento
                $stmt = $pdo->prepare("SELECT data_ajuizamento, valor_ajuizado FROM cbp_processos_judiciais WHERE id = ?");
                $stmt->execute([$processo_id]);
                $processo = $stmt->fetch();
                
                if (!$processo) {
                    throw new Exception('Processo não encontrado');
                }
                
                if ($processo['data_ajuizamento'] === null || $processo['valor_ajuizado'] === null) {
                    throw new Exception('Não é possível incluir acordo em um processo sem informações de ajuizamento');
                }
                
                $pdo->beginTransaction();
                
                try {
                    // Se deve inativar acordos anteriores
                    if ($inativar_anteriores) {
                        $stmt = $pdo->prepare("UPDATE cbp_acordos SET ativo = 0 WHERE processo_id = ?");
                        $stmt->execute([$processo_id]);
                        
                        // Manter o status como ACORDO JUDICIAL (2) mesmo ao inativar acordos anteriores
                        $stmt = $pdo->prepare("UPDATE cbp_processos_judiciais SET status_id = 2 WHERE id = ?");
                        $stmt->execute([$processo_id]);
                    } else {
                        // Se é o primeiro acordo, atualizar status para ACORDO JUDICIAL (2)
                        $stmt = $pdo->prepare("UPDATE cbp_processos_judiciais SET status_id = 2 WHERE id = ?");
                        $stmt->execute([$processo_id]);
                    }
                    
                    $pdo->commit();
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'Status do processo atualizado com sucesso!'
                    ]);
                } catch (Exception $e) {
                    $pdo->rollBack();
                    throw $e;
                }
                break;

            case 'verificar_pode_editar_ajuizamento':
                $processo_id = intval($_POST['processo_id']);
                
                // Verificar se o processo tem acordos
                $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM cbp_acordos WHERE processo_id = ?");
                $stmt->execute([$processo_id]);
                $tem_acordos = $stmt->fetchColumn() > 0;
                
                // Verificar se tem alvarás (adicionar esta verificação se existir uma tabela de alvarás)
                $tem_alvaras = false;
                // Adicionar verificação de alvarás aqui se necessário
                
                echo json_encode([
                    'success' => true,
                    'pode_editar' => !($tem_acordos || $tem_alvaras),
                    'mensagem' => ($tem_acordos || $tem_alvaras) ? 
                        'Não é possível editar o ajuizamento pois este processo possui acordos ou alvarás' : 
                        'Pode editar o ajuizamento'
                ]);
                break;

            default:
                throw new Exception('Ação inválida');
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Erro ao processar a ação: ' . $e->getMessage()
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Método não permitido'
    ]);
} 
<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Validar dados recebidos
    if (empty($_POST['alvara_id']) || empty($_POST['processo_id']) || empty($_POST['data_recebimento']) || empty($_POST['valor'])) {
        throw new Exception('Todos os campos obrigatórios devem ser preenchidos.');
    }

    $alvara_id = intval($_POST['alvara_id']);
    $processo_id = intval($_POST['processo_id']);
    $data_recebimento = $_POST['data_recebimento'];
    $valor = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['valor']);
    $observacoes = $_POST['observacoes'] ?? null;

    // Validar se o alvará existe
    $stmt = $pdo->prepare("SELECT id FROM cbp_alvaras WHERE id = ? AND processo_id = ?");
    $stmt->execute([$alvara_id, $processo_id]);
    if (!$stmt->fetch()) {
        throw new Exception('Alvará não encontrado ou não pertence a este processo.');
    }

    // Validar valor
    if (!is_numeric($valor) || floatval($valor) <= 0) {
        throw new Exception('Valor inválido');
    }

    // Atualizar alvará
    $stmt = $pdo->prepare("
        UPDATE cbp_alvaras
        SET 
            valor = ?,
            data_recebimento = ?,
            observacoes = ?,
            updated_at = NOW()
        WHERE id = ? AND processo_id = ?
    ");

    $stmt->execute([
        $valor,
        $data_recebimento,
        $observacoes,
        $alvara_id,
        $processo_id
    ]);

    echo json_encode([
        'success' => true,
        'message' => 'Alvará atualizado com sucesso!'
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao atualizar alvará no banco de dados.'
    ]);
    error_log($e->getMessage());
} 
<?php
require_once '../../config/database.php';

header('Content-Type: application/json');

// Obter o ID do associado
$id = $_GET['id'] ?? null;

try {
    if ($id) {
        // Preparar a consulta
        $stmt = $pdo->prepare("
            SELECT 
                a.id,
                a.nome,
                a.cpf_cnpj,
                a.pa_id,
                a.numero_contato,
                a.ativo,
                pa.nome as pa_nome,
                pa.numero as pa_numero
            FROM cbp_associados a
            LEFT JOIN pontos_atendimento pa ON a.pa_id = pa.id
            WHERE a.id = :id
        ");
        
        // Executar a consulta
        $stmt->execute(['id' => $id]);
        
        // Obter resultado
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            // Formatar CPF/CNPJ
            $doc = preg_replace('/[^0-9]/', '', $result['cpf_cnpj']);
            if (strlen($doc) === 11) {
                $result['cpf_cnpj_formatado'] = preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $doc);
            } else if (strlen($doc) === 14) {
                $result['cpf_cnpj_formatado'] = preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $doc);
            } else {
                $result['cpf_cnpj_formatado'] = $doc;
            }
            
            echo json_encode([
                'success' => true,
                'associado' => $result
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Associado não encontrado.'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'ID do associado não fornecido.'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao buscar associado: ' . $e->getMessage()
    ]);
} 
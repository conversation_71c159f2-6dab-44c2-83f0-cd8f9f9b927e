<?php
// Habilitar exibição de erros para debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../../auth_check.php';
require_once '../../config/database.php';

// Log para debug
error_log('Iniciando buscar_contratos.php');
error_log('processo_id recebido: ' . (isset($_GET['processo_id']) ? $_GET['processo_id'] : 'não definido'));
error_log('REQUEST_URI: ' . $_SERVER['REQUEST_URI']);
error_log('SCRIPT_FILENAME: ' . $_SERVER['SCRIPT_FILENAME']);

header('Content-Type: application/json');

try {
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $processo_id = isset($_GET['processo_id']) ? intval($_GET['processo_id']) : 0;

    if (!$processo_id) {
        throw new Exception('ID do processo não fornecido');
    }

    // Buscar informações do processo e CPF/CNPJ do associado
    $stmt = $pdo->prepare("
        SELECT p.associado_documento
        FROM cbp_processos_judiciais p
        WHERE p.id = ?
    ");
    $stmt->execute([$processo_id]);
    $processo = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$processo) {
        throw new Exception('Processo não encontrado');
    }

    // Buscar o ID do associado pelo CPF/CNPJ
    $stmt = $pdo->prepare("
        SELECT id
        FROM cbp_associados
        WHERE cpf_cnpj = ?
    ");
    $stmt->execute([$processo['associado_documento']]);
    $associado = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$associado) {
        throw new Exception('Associado não encontrado');
    }
    
    $associado_id = $associado['id'];
    error_log('ID do associado: ' . $associado_id);

    $limit = 10;
    $offset = ($page - 1) * $limit;

    // Log dos parâmetros recebidos
    error_log('Parâmetros recebidos:');
    error_log('search: ' . $search);
    error_log('page: ' . $page);
    error_log('processo_id: ' . $processo_id);

    // Query para buscar contratos do associado não vinculados
    $sql = "
        SELECT 
            c.id,
            c.numero_contrato,
            m.nome as modalidade_nome
        FROM 
            cbp_contratos c
        JOIN
            cbp_associados a ON c.associado_id = a.id
        LEFT JOIN
            cbp_modalidades_processo m ON c.modalidade_id = m.id
        WHERE 
            c.associado_id = :associado_id
            AND NOT EXISTS (
                SELECT 1 
                FROM cbp_processos_contratos pc 
                WHERE pc.contrato_id = c.id
            )
    ";

    // Adiciona condições de busca se houver termo de pesquisa
    if (!empty($search)) {
        $sql .= " AND (
            c.numero_contrato LIKE :search 
            OR m.nome LIKE :search
        )";
    }

    // Adiciona ordenação e paginação
    $sql .= " ORDER BY c.numero_contrato ASC LIMIT :limit OFFSET :offset";

    // Log da query
    error_log('Query SQL: ' . $sql);

    $stmt = $pdo->prepare($sql);

    // Bind dos parâmetros
    $stmt->bindParam(':associado_id', $associado_id, PDO::PARAM_INT);
    if (!empty($search)) {
        $searchTerm = "%{$search}%";
        $stmt->bindParam(':search', $searchTerm, PDO::PARAM_STR);
    }
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);

    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Log dos resultados
    error_log('Resultados encontrados: ' . count($results));
    error_log('Primeiro resultado: ' . print_r($results[0] ?? 'Nenhum resultado', true));

    // Query para contar total de registros
    $countSql = "
        SELECT COUNT(*) as total 
        FROM cbp_contratos c
        JOIN cbp_associados a ON c.associado_id = a.id
        LEFT JOIN cbp_modalidades_processo m ON c.modalidade_id = m.id
        WHERE 
            c.associado_id = :associado_id
            AND NOT EXISTS (
                SELECT 1 
                FROM cbp_processos_contratos pc 
                WHERE pc.contrato_id = c.id
            )
    ";

    if (!empty($search)) {
        $countSql .= " AND (
            c.numero_contrato LIKE :search 
            OR m.nome LIKE :search
        )";
    }

    $countStmt = $pdo->prepare($countSql);
    $countStmt->bindParam(':associado_id', $associado_id, PDO::PARAM_INT);
    if (!empty($search)) {
        $countStmt->bindParam(':search', $searchTerm, PDO::PARAM_STR);
    }
    $countStmt->execute();
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Log do total
    error_log('Total de registros: ' . $total);

    // Formata os resultados
    $formattedResults = array_map(function($row) {
        return [
            'id' => $row['id'],
            'text' => $row['numero_contrato'] . ' - ' . ($row['modalidade_nome'] ?? 'Sem modalidade')
        ];
    }, $results);

    echo json_encode([
        'results' => $formattedResults,
        'pagination' => [
            'more' => ($offset + $limit) < $total
        ]
    ]);

} catch (Exception $e) {
    error_log('Erro em buscar_contratos.php: ' . $e->getMessage());
    error_log('Stack trace: ' . $e->getTraceAsString());
    error_log('Query que causou o erro: ' . ($sql ?? 'SQL não definida'));
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => $e->getMessage()
    ]);
}

function formatarDocumento($documento) {
    $documento = preg_replace('/[^0-9]/', '', $documento);
    if (strlen($documento) == 11) {
        return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $documento);
    } elseif (strlen($documento) == 14) {
        return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $documento);
    }
    return $documento;
} 
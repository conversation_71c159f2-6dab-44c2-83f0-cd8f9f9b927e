<?php
require_once '../auth_check.php';
require_once '../config/database.php';
require_once '../includes/class/Honorarios.php';

try {
    // Validar parâmetros
    $honorario_id = isset($_GET['honorario_id']) ? intval($_GET['honorario_id']) : 0;
    
    if (!$honorario_id) {
        throw new Exception('ID do honorário não fornecido');
    }

    // Buscar detalhes do honorário
    $stmt = $pdo->prepare("
        SELECT 
            h.*,
            a.nome AS nome_associado,
            adv.nome AS nome_advogado,
            p.numero_processo,
            CASE 
                WHEN h.tipo = 'PARCELA' THEN CONCAT('Parcela ', h.numero_parcela, '/', h.total_parcelas)
                WHEN h.tipo = 'ENTRADA' THEN 'Entrada'
                WHEN h.tipo = 'ALVARA' THEN 'Alvará'
                ELSE h.tipo
            END as descricao_tipo,
            CASE 
                WHEN h.status = 'PENDENTE' THEN 'badge bg-warning'
                WHEN h.status = 'PAGO' THEN 'badge bg-success'
                WHEN h.status = 'CANCELADO' THEN 'badge bg-danger'
                ELSE 'badge bg-secondary'
            END as status_class
        FROM cbp_honorarios h
        LEFT JOIN cbp_associados a ON h.associado_id = a.id
        LEFT JOIN cbp_advogados adv ON h.advogado_id = adv.id
        LEFT JOIN cbp_processos_judiciais p ON h.processo_id = p.id
        WHERE h.id = ?
    ");
    
    $stmt->execute([$honorario_id]);
    $honorario = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$honorario) {
        throw new Exception('Honorário não encontrado');
    }

    // Formatar valores
    $honorario['valor_recebido_formatado'] = 'R$ ' . number_format($honorario['valor_recebido'], 2, ',', '.');
    $honorario['valor_honorario_formatado'] = 'R$ ' . number_format($honorario['valor_honorario'], 2, ',', '.');
    $honorario['porcentagem_formatada'] = number_format($honorario['porcentagem_honorario'], 4, ',', '.') . '%';
    $honorario['data_recebimento_formatada'] = date('d/m/Y', strtotime($honorario['data_recebimento']));
    $honorario['created_at_formatado'] = date('d/m/Y H:i:s', strtotime($honorario['created_at']));

    // Retornar sucesso
    echo json_encode([
        'success' => true,
        'data' => $honorario
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 
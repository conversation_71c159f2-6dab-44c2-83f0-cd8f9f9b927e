<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
        throw new Exception('ID do PA não informado ou inválido');
    }

    $stmt = $pdo->prepare("SELECT id, nome FROM pontos_atendimento WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $pa = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$pa) {
        throw new Exception('PA não encontrado');
    }

    echo json_encode([
        'success' => true,
        'nome' => $pa['nome']
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 
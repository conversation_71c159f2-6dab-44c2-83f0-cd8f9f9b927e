

2025-04-02 14:58:33 - Nova requisição
POST data: Array
(
    [id] => 6
    [pa_id] => 17
    [nome] => Teste 4
    [cpf_cnpj] => 15.616.161/6516-51
    [numero_processo] => 2222222
    [numero_contrato] => 333333
    [valor_ajuizado] => R$ 98.789.700,00
    [data_envio] => 2025-03-03
    [data_ajuizamento] => 2025-03-11
    [modalidade_id] => 40
    [advogado_id] => 2
    [observacoes] => 
    [voltou_executar] => 1
)

Verificação de voltou_executar: Campo existe
Valor original do campo voltou_executar: 1
Resultado da verificação: true
Caminho: voltou_executar = true, status_id = 4
Inativando acordos para o processo 6
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Erro: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sicoob_access_control.cbp_historico_status' doesn't exist
Trace: #0 C:\xampp\htdocs\d\cobranca\ajax\salvar_processo.php(175): PDOStatement->execute(Array)
#1 {main}


2025-04-02 15:04:15 - Nova requisição
POST data: Array
(
    [id] => 6
    [pa_id] => 17
    [nome] => Teste 4
    [cpf_cnpj] => 15.616.161/6516-51
    [numero_processo] => 2222222
    [numero_contrato] => 333333
    [valor_ajuizado] => R$ 98.789.700,00
    [data_envio] => 2025-03-03
    [data_ajuizamento] => 2025-03-11
    [modalidade_id] => 40
    [advogado_id] => 2
    [observacoes] => 
    [voltou_executar] => 1
)

Verificação de voltou_executar: Campo existe
Valor original do campo voltou_executar: 1
Resultado da verificação: true
Caminho: voltou_executar = true, status_id = 4
Inativando acordos para o processo 6
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-02 15:05:33 - Nova requisição
POST data: Array
(
    [id] => 10
    [pa_id] => 12
    [nome] => TESTE 10
    [cpf_cnpj] => 10.101.010/1010-10
    [numero_processo] => 2123456-78
    [numero_contrato] => 11111-1
    [valor_ajuizado] => R$ 5.000.000,00
    [data_envio] => 2025-01-08
    [data_ajuizamento] => 2025-03-11
    [modalidade_id] => 10
    [advogado_id] => 3
    [observacoes] => TESTE
    [voltou_executar] => 0
)

Verificação de voltou_executar: Campo existe
Valor original do campo voltou_executar: 0
Resultado da verificação: false
Caminho: voltou_executar = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Executando query UPDATE com status_id = 1
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 1


2025-04-02 15:05:48 - Nova requisição
POST data: Array
(
    [id] => 6
    [pa_id] => 17
    [nome] => Teste 4
    [cpf_cnpj] => 15.616.161/6516-51
    [numero_processo] => 2222222
    [numero_contrato] => 333333
    [valor_ajuizado] => R$ 9.878.970.000,00
    [data_envio] => 2025-03-03
    [data_ajuizamento] => 2025-03-11
    [modalidade_id] => 40
    [advogado_id] => 2
    [observacoes] => 
    [voltou_executar] => 0
)

Verificação de voltou_executar: Campo existe
Valor original do campo voltou_executar: 0
Resultado da verificação: false
Caminho: voltou_executar = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Executando query UPDATE com status_id = 1
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 1


2025-04-02 15:10:56 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 67.676.800,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [voltou_executar] => 1
)

Verificação de voltou_executar: Campo existe
Valor original do campo voltou_executar: 1
Resultado da verificação: true
Caminho: voltou_executar = true, status_id = 4
Inativando acordos para o processo 5
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-02 15:11:04 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 6.767.680.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [voltou_executar] => 0
)

Verificação de voltou_executar: Campo existe
Valor original do campo voltou_executar: 0
Resultado da verificação: false
Caminho: voltou_executar = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Executando query UPDATE com status_id = 1
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 1


2025-04-02 15:18:12 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 676.768.000.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 1
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 5
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-02 15:24:15 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 67.676.800.000.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Executando query UPDATE com status_id = 1
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 1


2025-04-02 15:26:46 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 999.999.999.999.999,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 1
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 5
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-02 15:27:31 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 999.999.999.999.999,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 2


2025-04-02 15:28:12 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 999.999.999.999.999,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 2


2025-04-02 15:30:45 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 999.999.999.999.999,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 1
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 5
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-02 15:33:04 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 999.999.999.999.999,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 2


2025-04-02 15:33:37 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 1.651.515,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 2


2025-04-02 15:33:44 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 165.151.500,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 1
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 5
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-02 15:34:03 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 15.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 1
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 5
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 4


2025-04-02 15:34:36 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 1.500.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 2


2025-04-02 15:35:16 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 150.000.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 2


2025-04-02 15:35:25 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 1.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 2


2025-04-02 15:35:30 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 100.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 2


2025-04-02 15:36:47 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 1.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 2


2025-04-02 15:37:39 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 100.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 2


2025-04-02 15:38:18 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 1.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 2


2025-04-02 15:41:11 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 1.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 1
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 5
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-02 17:39:55 - Nova requisição
POST data: Array
(
    [id] => 5
    [pa_id] => 19
    [nome] => Teste 3
    [cpf_cnpj] => 02.508.505/0560-80
    [numero_processo] => 577668
    [numero_contrato] => 043546
    [valor_ajuizado] => R$ 1.000,00
    [data_envio] => 2025-03-04
    [data_ajuizamento] => 2025-03-19
    [modalidade_id] => 28
    [advogado_id] => 4
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 2


2025-04-02 17:40:10 - Nova requisição
POST data: Array
(
    [id] => 6
    [pa_id] => 17
    [nome] => Teste 4
    [cpf_cnpj] => 15.616.161/6516-51
    [numero_processo] => 2222222
    [numero_contrato] => 333333
    [valor_ajuizado] => R$ 987.897,00
    [data_envio] => 2025-03-03
    [data_ajuizamento] => 2025-03-11
    [modalidade_id] => 40
    [advogado_id] => 2
    [observacoes] => 
    [retomado] => 0
)

Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Executando query UPDATE com status_id = 1
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 1


2025-04-02 20:43:00 - Nova requisição
POST data: Array
(
    [id] => 12
    [pa_id] => 22
    [nome] => TESTE PEDRO HENRIQUE DE ASSUMPÇÃO GOMES
    [cpf_cnpj] => 15.151.561/5615-61
    [numero_processo] => 243545656
    [numero_contrato] => 34546456
    [valor_ajuizado] => R$ 50.000,00
    [data_envio] => 2023-08-01
    [modalidade_id] => 14
    [advogado_id] => 3
    [observacoes] => 
    [retomado] => 0
)

Erro: Campo obrigatório não informado: data_ajuizamento
Trace: #0 {main}


2025-04-02 22:50:07 - Nova requisição
POST data: Array
(
    [id] => 12
    [pa_id] => 22
    [nome] => TESTE PEDRO HENRIQUE DE ASSUMPÇÃO GOMES
    [cpf_cnpj] => 15.151.561/5615-61
    [numero_processo] => 243545656
    [numero_contrato] => 34546456
    [data_envio] => 2023-08-01
    [modalidade_id] => 14
    [advogado_id] => 3
    [observacoes] => 
    [retomado] => 1
)

Data ajuizamento obtida do banco: 2024-11-13
Valor ajuizado obtido do banco: 38000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 12
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-02 22:57:42 - Nova requisição
POST data: Array
(
    [id] => 8
    [pa_id] => 16
    [nome] => Teste 7
    [cpf_cnpj] => 77.777.777/7777-77
    [numero_processo] => 777
    [numero_contrato] => 777-1
    [data_envio] => 2024-07-07
    [modalidade_id] => 41
    [advogado_id] => 1
    [observacoes] => 
    [retomado] => 1
)

Data ajuizamento obtida do banco: 2025-01-07
Valor ajuizado obtido do banco: 70000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 8
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-02 22:58:42 - Nova requisição
POST data: Array
(
    [id] => 13
    [pa_id] => 23
    [nome] => TESTE 86786776776
    [cpf_cnpj] => 67.676.756/7665-77
    [numero_processo] => 32535
    [numero_contrato] => 345436
    [data_envio] => 2025-04-02
    [modalidade_id] => 14
    [advogado_id] => 2
    [observacoes] => 
    [retomado] => 0
)

Data ajuizamento obtida do banco: 2025-03-30
Valor ajuizado obtido do banco: 20000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Executando query UPDATE com status_id = 1
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 1


2025-04-02 23:00:56 - Nova requisição
POST data: Array
(
    [id] => 13
    [pa_id] => 23
    [nome] => TESTE 86786776776
    [cpf_cnpj] => 67.676.756/7665-77
    [numero_processo] => 32535
    [numero_contrato] => 345436
    [data_envio] => 2025-04-02
    [modalidade_id] => 14
    [advogado_id] => 2
    [observacoes] => 
    [retomado] => 1
)

Data ajuizamento obtida do banco: 2025-03-30
Valor ajuizado obtido do banco: 20000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 13
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-03 15:02:58 - Nova requisição
POST data: Array
(
    [id] => 10
    [pa_id] => 12
    [nome] => TESTE 10
    [cpf_cnpj] => 10.101.010/1010-10
    [numero_processo] => 2123456-78
    [numero_contrato] => 11111-1
    [data_envio] => 2025-01-08
    [modalidade_id] => 10
    [advogado_id] => 3
    [observacoes] => TESTE
    [retomado] => 0
)

Data ajuizamento obtida do banco: 2025-03-11
Valor ajuizado obtido do banco: 5000000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Executando query UPDATE com status_id = 1
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 1


2025-04-03 15:03:12 - Nova requisição
POST data: Array
(
    [id] => 10
    [pa_id] => 12
    [nome] => TESTE 10
    [cpf_cnpj] => 10.101.010/1010-10
    [numero_processo] => 2123456-78
    [numero_contrato] => 11111-1
    [data_envio] => 2025-01-08
    [modalidade_id] => 10
    [advogado_id] => 3
    [observacoes] => TESTE
    [retomado] => 0
)

Data ajuizamento obtida do banco: 2025-03-11
Valor ajuizado obtido do banco: 500000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Executando query UPDATE com status_id = 1
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Status alterado com sucesso. status_id: 1


2025-04-03 15:43:47 - Nova requisição
POST data: Array
(
    [id] => 13
    [pa_id] => 23
    [nome] => TESTE 86786776776
    [cpf_cnpj] => 67.676.756/7665-77
    [numero_processo] => 32535
    [numero_contrato] => 345436
    [data_envio] => 2025-04-02
    [modalidade_id] => 14
    [advogado_id] => 2
    [observacoes] => 
    [retomado] => 0
)

Data ajuizamento obtida do banco: 2025-03-30
Valor ajuizado obtido do banco: 20000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Executando query UPDATE com status_id = 1
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 1


2025-04-03 15:43:57 - Nova requisição
POST data: Array
(
    [id] => 8
    [pa_id] => 16
    [nome] => Teste 7
    [cpf_cnpj] => 77.777.777/7777-77
    [numero_processo] => 777
    [numero_contrato] => 777-1
    [data_envio] => 2024-07-07
    [modalidade_id] => 41
    [advogado_id] => 1
    [observacoes] => 
    [retomado] => 0
)

Data ajuizamento obtida do banco: 2025-01-07
Valor ajuizado obtido do banco: 70000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Executando query UPDATE com status_id = 1
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 1


2025-04-03 16:39:06 - Nova requisição
POST data: Array
(
    [id] => 12
    [pa_id] => 22
    [nome] => TESTE PEDRO HENRIQUE DE ASSUMPÇÃO GOMES
    [cpf_cnpj] => 15.151.561/5615-61
    [numero_processo] => 243545656
    [numero_contrato] => 34546456
    [data_envio] => 2023-08-01
    [modalidade_id] => 14
    [advogado_id] => 3
    [observacoes] => 
    [retomado] => 0
)

Data ajuizamento obtida do banco: 2024-11-13
Valor ajuizado obtido do banco: 38000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Executando query UPDATE com status_id = 1
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 1


2025-04-03 17:58:28 - Nova requisição
POST data: Array
(
    [id] => 13
    [pa_id] => 23
    [nome] => TESTE 86786776776
    [cpf_cnpj] => 67.676.756/7665-77
    [numero_processo] => 32535
    [numero_contrato] => 345436
    [data_envio] => 2025-04-02
    [modalidade_id] => 14
    [advogado_id] => 2
    [observacoes] => 
    [retomado] => 1
)

Data ajuizamento obtida do banco: 2025-03-30
Valor ajuizado obtido do banco: 20000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 13
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-03 22:16:57 - Nova requisição
POST data: Array
(
    [id] => 13
    [pa_id] => 23
    [nome] => TESTE 86786776776
    [cpf_cnpj] => 67.676.756/7665-77
    [numero_processo] => 32535
    [numero_contrato] => 345436
    [data_envio] => 2025-04-02
    [modalidade_id] => 14
    [advogado_id] => 2
    [observacoes] => 
    [retomado] => 1
)

Data ajuizamento obtida do banco: 2025-03-30
Valor ajuizado obtido do banco: 20000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 13
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-04 15:35:47 - Nova requisição
POST data: Array
(
    [id] => 13
    [pa_id] => 23
    [nome] => TESTE 86786776776
    [cpf_cnpj] => 67.676.756/7665-77
    [numero_processo] => 32535
    [numero_contrato] => 345436
    [data_envio] => 2025-04-02
    [modalidade_id] => 14
    [advogado_id] => 2
    [observacoes] => 
    [retomado] => 1
)

Data ajuizamento obtida do banco: 2025-03-30
Valor ajuizado obtido do banco: 20000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 13
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-04 15:43:13 - Nova requisição
POST data: Array
(
    [id] => 14
    [pa_id] => 12
    [nome] => TESTE CAROL
    [cpf_cnpj] => 16.186.165/1818-19
    [numero_processo] => 115156
    [numero_contrato] => 234234
    [data_envio] => 2025-04-01
    [modalidade_id] => 4
    [advogado_id] => 1
    [observacoes] => 
    [retomado] => 1
)

Data ajuizamento obtida do banco: 2025-04-04
Valor ajuizado obtido do banco: 20000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 1
Resultado da verificação: true
Caminho: retomado = true, status_id = 4
Inativando acordos para o processo 14
Executando query UPDATE com status_id = 4
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 4


2025-04-04 15:43:44 - Nova requisição
POST data: Array
(
    [id] => 14
    [pa_id] => 12
    [nome] => TESTE CAROL
    [cpf_cnpj] => 16.186.165/1818-19
    [numero_processo] => 115156
    [numero_contrato] => 234234
    [data_envio] => 2025-04-01
    [modalidade_id] => 4
    [advogado_id] => 1
    [observacoes] => 
    [retomado] => 0
)

Data ajuizamento obtida do banco: 2025-04-04
Valor ajuizado obtido do banco: 20000.00
Verificação de retomado: Campo existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Sim
Reativando acordo mais recente
Definindo status_id = 2 (ACORDO JUDICIAL)
Executando query UPDATE com status_id = 2
Resultado da execução do UPDATE: Sucesso
Linhas afetadas: 1
Histórico de status registrado.
Status alterado com sucesso. status_id: 2


2025-04-04 22:10:24 - Nova requisição
POST data: Array
(
    [id] => 16
    [data_distribuicao] => 2025-04-04
    [advogado_id] => 1
)

Erro: Campo obrigatório não informado: nome
Trace: #0 {main}


2025-04-07 16:16:45 - Nova requisição
POST data: Array
(
    [id] => 16
    [data_envio] => 2025-04-04
    [advogado_id] => 1
    [observacoes] => 
    [numero_processo] => 1345545-67.8464.3.76.7763
    [data_ajuizamento] => 2025-04-07
    [valor_ajuizado] => R$ 10.000,00
    [status_id] => 1
    [data_status] => 2025-04-07
    [motivo_status] => 
)

Erro: Campo obrigatório não informado: id
Trace: #0 {main}


2025-04-07 16:16:53 - Nova requisição
POST data: Array
(
    [id] => 16
    [data_envio] => 2025-04-04
    [advogado_id] => 1
    [observacoes] => 
    [numero_processo] => 1345545-67.8464.3.76.7763
    [data_ajuizamento] => 2025-04-07
    [valor_ajuizado] => R$ 10.000,00
    [status_id] => 1
    [data_status] => 2025-04-07
    [motivo_status] => 
)

Erro: Campo obrigatório não informado: id
Trace: #0 {main}


2025-04-07 16:17:45 - Nova requisição
POST data: Array
(
    [id] => 16
    [data_envio] => 2025-04-04
    [advogado_id] => 1
    [observacoes] => 
    [numero_processo] => 5354657-67.4122.4.34.5364
    [data_ajuizamento] => 2025-04-07
    [valor_ajuizado] => R$ 10.000,00
    [status_id] => 1
    [data_status] => 2025-04-07
    [motivo_status] => 
)

Erro: Campo obrigatório não informado: id
Trace: #0 {main}


2025-04-07 16:26:58 - Nova requisição
POST data: Array
(
    [id] => 16
    [data_envio] => 2025-04-04
    [advogado_id] => 1
    [observacoes] => 
    [numero_processo] => 3243234-54.3546.5.75.6768
    [data_ajuizamento] => 2025-04-07
    [valor_ajuizado] => R$ 10.000,00
)

Erro: Campo obrigatório não informado: id
Trace: #0 {main}


2025-04-07 16:27:06 - Nova requisição
POST data: Array
(
    [id] => 16
    [data_envio] => 2025-04-04
    [advogado_id] => 1
    [observacoes] => 
    [numero_processo] => 3243234-54.3546.5.75.6768
    [data_ajuizamento] => 2025-04-07
    [valor_ajuizado] => R$ 10.000,00
)

Erro: Campo obrigatório não informado: id
Trace: #0 {main}


2025-04-07 16:27:40 - Nova requisição
POST data: Array
(
    [id] => 16
    [data_envio] => 2025-04-04
    [advogado_id] => 1
    [observacoes] => 
    [numero_processo] => 3243234-54.3546.5.75.6768
    [data_ajuizamento] => 2025-04-07
    [valor_ajuizado] => R$ 10.000,00
)

Erro: Campo obrigatório não informado: id
Trace: #0 {main}


2025-04-07 16:28:52 - Nova requisição
POST data: Array
(
    [id] => 16
    [data_envio] => 2025-04-04
    [advogado_id] => 1
    [observacoes] => 
    [numero_processo] => 4354657-68.6784.6.24.6456
    [data_ajuizamento] => 2025-04-07
    [valor_ajuizado] => R$ 10.000,00
)

Erro: Campo obrigatório não informado: id
Trace: #0 {main}


2025-04-07 16:32:45 - Nova requisição
POST data: Array
(
    [id] => 16
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 13
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-07
    [data_envio] => 2025-04-04
    [advogado_id] => 1
    [observacoes] => 
    [numero_processo] => 3467687-87.6365.5.36.5756
    [data_ajuizamento] => 2025-04-07
    [valor_ajuizado] => R$ 10.000,00
)

Verificação de retomado: Campo não existe
Valor original do campo retomado: 0
Resultado da verificação: false
Caminho: retomado = false
Tem acordo? Não
Definindo status_id = 1 (VIGENTE)
Erro: CPF/CNPJ inválido
Trace: #0 {main}


2025-04-10 19:56:03 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-10
    [data_envio] => 2025-04-08
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)



2025-04-10 19:57:04 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-10
    [data_envio] => 2025-04-08
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)



2025-04-10 19:58:26 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-10
    [data_envio] => 2025-04-08
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 18
Status retomado: Sim
Status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-08
    [advogado_id] => 1
    [id] => 18
)

Transação revertida devido a erro
Erro: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'data_atualizacao' in 'field list'
Stack trace: #0 C:\xampp\htdocs\d\cobranca\ajax\salvar_processo.php(62): PDOStatement->execute(Array)
#1 {main}


2025-04-10 20:02:16 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-10
    [data_envio] => 2025-04-08
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 18
Status retomado: Sim
Status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-08
    [advogado_id] => 1
    [id] => 18
)

Inativando acordos do processo 18
Transação revertida devido a erro
Erro: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'status' in 'where clause'
Stack trace: #0 C:\xampp\htdocs\d\cobranca\ajax\salvar_processo.php(75): PDOStatement->execute(Array)
#1 {main}


2025-04-10 20:03:42 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-10
    [data_envio] => 2025-04-08
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 0
)

ID do processo: 18
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-08
    [advogado_id] => 1
    [id] => 18
)

Parâmetros do histórico: Array
(
    [processo_id] => 18
    [status_id] => 2
    [usuario_id] => 1
)

Transação revertida devido a erro
Erro: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'usuario_id' in 'field list'
Stack trace: #0 C:\xampp\htdocs\d\cobranca\ajax\salvar_processo.php(97): PDOStatement->execute(Array)
#1 {main}


2025-04-10 20:07:52 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-10
    [data_envio] => 2025-04-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 0
)

ID do processo: 18
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-07
    [advogado_id] => 1
    [id] => 18
)

Parâmetros do histórico: Array
(
    [processo_id] => 18
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-10 20:08:18 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-10
    [data_envio] => 2025-04-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 18
Status retomado: Sim
Status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-07
    [advogado_id] => 1
    [id] => 18
)

Inativando acordos do processo 18
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 18
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-10 20:46:50 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-10
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 4
    [retomado] => 1
)

ID do processo: 18
Status retomado: Sim
Status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [id] => 18
)

Inativando acordos do processo 18
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 18
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-10 22:23:51 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-10
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 18
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [id] => 18
)

Parâmetros do histórico: Array
(
    [processo_id] => 18
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-10 22:53:52 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-10
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 18
Status retomado: Sim
Status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [id] => 18
)

Inativando acordos do processo 18
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 18
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-11 13:42:45 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-11
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 18
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [id] => 18
)

Parâmetros do histórico: Array
(
    [processo_id] => 18
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-11 13:49:03 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-11
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 18
Status retomado: Sim
Status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [id] => 18
)

Inativando acordos do processo 18
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 18
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-11 14:04:04 - Nova requisição
POST data: Array
(
    [id] => 17
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 1
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-11
    [data_envio] => 2025-04-07
    [advogado_id] => 3
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 17
Status retomado: Sim
Status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-07
    [advogado_id] => 3
    [id] => 17
)

Inativando acordos do processo 17
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 17
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-11 14:04:17 - Nova requisição
POST data: Array
(
    [id] => 17
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 1
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-11
    [data_envio] => 2025-04-07
    [advogado_id] => 3
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 17
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-07
    [advogado_id] => 3
    [id] => 17
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 83), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 17
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-11 14:10:06 - Nova requisição
POST data: Array
(
    [id] => 17
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 1
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-11
    [data_envio] => 2025-04-07
    [advogado_id] => 3
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 17
Status retomado: Sim
Status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-07
    [advogado_id] => 3
    [id] => 17
)

Inativando acordos do processo 17
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 17
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-11 14:10:25 - Nova requisição
POST data: Array
(
    [id] => 17
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 1
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-11
    [data_envio] => 2025-04-07
    [advogado_id] => 3
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 17
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-07
    [advogado_id] => 3
    [id] => 17
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 83), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 17
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-11 14:12:47 - Nova requisição
POST data: Array
(
    [id] => 18
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 11
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-11
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 18
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-10
    [advogado_id] => 1
    [id] => 18
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 82), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 18
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-14 20:11:55 - Nova requisição
POST data: Array
(
    [id] => 21
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 12
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-14
    [data_envio] => 2025-04-15
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 21
Status retomado: Sim
Status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-15
    [advogado_id] => 1
    [id] => 21
)

Inativando acordos do processo 21
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 21
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-15 14:09:13 - Nova requisição
POST data: Array
(
    [id] => 23
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 23
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-15
    [data_envio] => 2025-03-30
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 23
Status retomado: Sim
Status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-03-30
    [advogado_id] => 1
    [id] => 23
)

Inativando acordos do processo 23
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 23
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-15 14:09:36 - Nova requisição
POST data: Array
(
    [id] => 23
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 23
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-15
    [data_envio] => 2025-03-30
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 23
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-03-30
    [advogado_id] => 1
    [id] => 23
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 118), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 23
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-15 14:09:37 - Nova requisição
POST data: Array
(
    [id] => 23
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 23
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-15
    [data_envio] => 2025-03-30
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 23
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-03-30
    [advogado_id] => 1
    [id] => 23
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 118), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 23
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-15 14:09:54 - Nova requisição
POST data: Array
(
    [id] => 23
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 23
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-15
    [data_envio] => 2025-03-30
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 23
Status retomado: Sim
Status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-03-30
    [advogado_id] => 1
    [id] => 23
)

Inativando acordos do processo 23
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 23
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-24 17:28:35 - Nova requisição
POST data: Array
(
    [id] => 29
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 15
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-24
    [data_envio] => 2025-04-24
    [advogado_id] => 3
    [observacoes] => 
    [status_id] => 1
    [retomado] => 0
)

ID do processo: 29
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-24
    [advogado_id] => 3
    [id] => 29
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Nenhum acordo encontrado para reativar
Parâmetros do histórico: Array
(
    [processo_id] => 29
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-24 19:58:43 - Nova requisição
POST data: Array
(
    [id] => 33
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 13
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-24
    [data_envio] => 2025-04-24
    [advogado_id] => 4
    [observacoes] => 
    [status_id] => 1
    [retomado] => 0
)

ID do processo: 33
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-24
    [advogado_id] => 4
    [id] => 33
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Nenhum acordo encontrado para reativar
Parâmetros do histórico: Array
(
    [processo_id] => 33
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-24 21:35:03 - Nova requisição
POST data: Array
(
    [id] => 33
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 13
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-24
    [data_envio] => 2025-04-24
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 0
)

ID do processo: 33
Status retomado: Não
Status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-24
    [advogado_id] => 1
    [id] => 33
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Nenhum acordo encontrado para reativar
Parâmetros do histórico: Array
(
    [processo_id] => 33
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-24 21:39:14 - Nova requisição
POST data: Array
(
    [id] => 33
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 13
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-24
    [data_envio] => 2025-04-24
    [advogado_id] => 4
    [observacoes] => 
    [status_id] => 2
    [retomado] => 0
)

ID do processo: 33
Status retomado: Não
Tem acordos: Não
Status atual: 2
Novo status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-24
    [advogado_id] => 4
    [id] => 33
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Nenhum acordo encontrado para reativar
Parâmetros do histórico: Array
(
    [processo_id] => 33
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-24 21:39:57 - Nova requisição
POST data: Array
(
    [id] => 34
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 13
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-24
    [data_envio] => 2025-04-24
    [advogado_id] => 4
    [observacoes] => 
    [status_id] => 1
    [retomado] => 0
)

ID do processo: 34
Status retomado: Não
Tem acordos: Não
Status atual: 1
Novo status ID: 1
Parâmetros da atualização: Array
(
    [status_id] => 1
    [data_envio] => 2025-04-24
    [advogado_id] => 4
    [id] => 34
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Nenhum acordo encontrado para reativar
Parâmetros do histórico: Array
(
    [processo_id] => 34
    [status_id] => 1
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-24 21:40:09 - Nova requisição
POST data: Array
(
    [id] => 30
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 15
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-24
    [data_envio] => 2025-04-24
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 30
Status retomado: Sim
Tem acordos: Sim
Status atual: 2
Novo status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-24
    [advogado_id] => 1
    [id] => 30
)

Inativando acordos do processo 30
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 30
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-24 21:40:21 - Nova requisição
POST data: Array
(
    [id] => 30
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 15
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-24
    [data_envio] => 2025-04-24
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 30
Status retomado: Não
Tem acordos: Sim
Status atual: 4
Novo status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-24
    [advogado_id] => 1
    [id] => 30
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 147), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 30
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-25 15:24:38 - Nova requisição
POST data: Array
(
    [id] => 36
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 13
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-25
    [data_envio] => 2025-04-22
    [advogado_id] => 4
    [observacoes] => 
    [status_id] => 1
    [retomado] => 0
)

ID do processo: 36
Status retomado: Não
Tem acordos: Não
Status atual: 1
Novo status ID: 1
Parâmetros da atualização: Array
(
    [status_id] => 1
    [data_envio] => 2025-04-22
    [advogado_id] => 4
    [id] => 36
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Nenhum acordo encontrado para reativar
Parâmetros do histórico: Array
(
    [processo_id] => 36
    [status_id] => 1
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-25 15:36:19 - Nova requisição
POST data: Array
(
    [id] => 37
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 12
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-25
    [data_envio] => 2025-04-25
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 37
Status retomado: Sim
Tem acordos: Sim
Status atual: 2
Novo status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-25
    [advogado_id] => 1
    [id] => 37
)

Inativando acordos do processo 37
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 37
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-25 15:36:47 - Nova requisição
POST data: Array
(
    [id] => 37
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 12
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-25
    [data_envio] => 2025-04-25
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 37
Status retomado: Não
Tem acordos: Sim
Status atual: 4
Novo status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-25
    [advogado_id] => 1
    [id] => 37
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 152), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 37
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-25 20:56:06 - Nova requisição
POST data: Array
(
    [id] => 42
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 15
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-25
    [data_envio] => 2025-04-07
    [advogado_id] => 4
    [observacoes] => 
    [status_id] => 1
    [retomado] => 0
)

ID do processo: 42
Status retomado: Não
Tem acordos: Não
Status atual: 1
Novo status ID: 1
Parâmetros da atualização: Array
(
    [status_id] => 1
    [data_envio] => 2025-04-07
    [advogado_id] => 4
    [id] => 42
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Nenhum acordo encontrado para reativar
Parâmetros do histórico: Array
(
    [processo_id] => 42
    [status_id] => 1
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-25 21:01:54 - Nova requisição
POST data: Array
(
    [id] => 44
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 17
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-25
    [data_envio] => 2025-04-03
    [advogado_id] => 3
    [observacoes] => 
    [status_id] => 1
    [retomado] => 0
)

ID do processo: 44
Status retomado: Não
Tem acordos: Não
Status atual: 1
Novo status ID: 1
Parâmetros da atualização: Array
(
    [status_id] => 1
    [data_envio] => 2025-04-03
    [advogado_id] => 3
    [id] => 44
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Nenhum acordo encontrado para reativar
Parâmetros do histórico: Array
(
    [processo_id] => 44
    [status_id] => 1
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-25 21:02:50 - Nova requisição
POST data: Array
(
    [id] => 43
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 17
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-25
    [data_envio] => 2025-04-08
    [advogado_id] => 3
    [observacoes] => 
    [status_id] => 1
    [retomado] => 0
)

ID do processo: 43
Status retomado: Não
Tem acordos: Não
Status atual: 1
Novo status ID: 1
Parâmetros da atualização: Array
(
    [status_id] => 1
    [data_envio] => 2025-04-08
    [advogado_id] => 3
    [id] => 43
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Nenhum acordo encontrado para reativar
Parâmetros do histórico: Array
(
    [processo_id] => 43
    [status_id] => 1
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-25 21:08:33 - Nova requisição
POST data: Array
(
    [id] => 42
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 15
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-25
    [data_envio] => 2025-04-07
    [advogado_id] => 4
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 42
Status retomado: Sim
Tem acordos: Sim
Status atual: 2
Novo status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-07
    [advogado_id] => 4
    [id] => 42
)

Inativando acordos do processo 42
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 42
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-25 21:08:46 - Nova requisição
POST data: Array
(
    [id] => 42
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 15
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-25
    [data_envio] => 2025-04-07
    [advogado_id] => 4
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 42
Status retomado: Não
Tem acordos: Sim
Status atual: 4
Novo status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-04-07
    [advogado_id] => 4
    [id] => 42
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 157), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 42
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-04-25 21:08:58 - Nova requisição
POST data: Array
(
    [id] => 42
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 15
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-25
    [data_envio] => 2025-04-07
    [advogado_id] => 4
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 42
Status retomado: Sim
Tem acordos: Sim
Status atual: 2
Novo status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-07
    [advogado_id] => 4
    [id] => 42
)

Inativando acordos do processo 42
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 42
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-04-25 22:17:12 - Nova requisição
POST data: Array
(
    [id] => 47
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 12
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-04-25
    [data_envio] => 2025-04-22
    [advogado_id] => 3
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 47
Status retomado: Sim
Tem acordos: Sim
Status atual: 2
Novo status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-04-22
    [advogado_id] => 3
    [id] => 47
)

Inativando acordos do processo 47
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 47
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-05-07 17:23:53 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)



2025-05-07 17:24:06 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 0
)



2025-05-07 17:24:21 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)



2025-05-07 17:26:11 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)



2025-05-07 17:27:25 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 0
)



2025-05-07 17:27:37 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => Teste
    [status_id] => 2
    [retomado] => 0
)



2025-05-07 17:37:50 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 0
)



2025-05-07 17:53:41 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [data_envio] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 0
)

ID do processo: 65
Status retomado: Não
Tem acordos: Sim
Status atual: 2
Novo status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-05-07
    [advogado_id] => 1
    [id] => 65
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 184), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 65
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-05-07 17:53:50 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [data_envio] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 65
Status retomado: Sim
Tem acordos: Sim
Status atual: 2
Novo status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-05-07
    [advogado_id] => 1
    [id] => 65
)

Inativando acordos do processo 65
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 65
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-05-07 17:54:07 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [data_envio] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 65
Status retomado: Não
Tem acordos: Sim
Status atual: 4
Novo status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-05-07
    [advogado_id] => 1
    [id] => 65
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 184), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 65
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-05-07 17:54:25 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [data_envio] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 65
Status retomado: Sim
Tem acordos: Sim
Status atual: 2
Novo status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-05-07
    [advogado_id] => 1
    [id] => 65
)

Inativando acordos do processo 65
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 65
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-05-07 17:54:38 - Nova requisição
POST data: Array
(
    [id] => 65
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-07
    [data_envio] => 2025-05-07
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 4
    [retomado] => 0
)

ID do processo: 65
Status retomado: Não
Tem acordos: Sim
Status atual: 4
Novo status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-05-07
    [advogado_id] => 1
    [id] => 65
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 184), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 65
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-05-20 19:38:49 - Nova requisição
POST data: Array
(
    [id] => 185
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 1
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-20
    [data_envio] => 2025-05-20
    [advogado_id] => 4
    [observacoes] => 
    [status_id] => 2
    [retomado] => 0
)

ID do processo: 185
Status retomado: Não
Tem acordos: Sim
Status atual: 2
Novo status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-05-20
    [advogado_id] => 4
    [id] => 185
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 301), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 185
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-05-22 21:02:25 - Nova requisição
POST data: Array
(
    [id] => 416
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 2
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-22
    [data_envio] => 2025-05-22
    [advogado_id] => 1
    [observacoes] => 
    [status_id] => 2
    [retomado] => 1
)

ID do processo: 416
Status retomado: Sim
Tem acordos: Sim
Status atual: 2
Novo status ID: 4
Parâmetros da atualização: Array
(
    [status_id] => 4
    [data_envio] => 2025-05-22
    [advogado_id] => 1
    [id] => 416
)

Inativando acordos do processo 416
Status INATIVO encontrado com ID: 4
Acordos inativados com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 416
    [status_id] => 4
    [observacoes] => Processo marcado como RETOMADO
)

Transação commitada com sucesso


2025-05-22 21:05:17 - Nova requisição
POST data: Array
(
    [id] => 419
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 15
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-22
    [data_envio] => 2025-05-22
    [advogado_id] => 2
    [observacoes] => 
    [status_id] => 1
    [retomado] => 0
)

ID do processo: 419
Status retomado: Não
Tem acordos: Não
Status atual: 1
Novo status ID: 1
Parâmetros da atualização: Array
(
    [status_id] => 1
    [data_envio] => 2025-05-22
    [advogado_id] => 2
    [id] => 419
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Nenhum acordo encontrado para reativar
Parâmetros do histórico: Array
(
    [processo_id] => 419
    [status_id] => 1
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-05-22 21:32:41 - Nova requisição
POST data: Array
(
    [id] => 427
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 15
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-22
    [data_envio] => 2025-05-22
    [advogado_id] => 2
    [observacoes] => 
    [status_id] => 1
    [retomado] => 0
)

ID do processo: 427
Status retomado: Não
Tem acordos: Não
Status atual: 1
Novo status ID: 1
Parâmetros da atualização: Array
(
    [status_id] => 1
    [data_envio] => 2025-05-22
    [advogado_id] => 2
    [id] => 427
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Nenhum acordo encontrado para reativar
Parâmetros do histórico: Array
(
    [processo_id] => 427
    [status_id] => 1
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-05-23 22:42:07 - Nova requisição
POST data: Array
(
    [id] => 470
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 5
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-23
    [data_envio] => 2025-05-23
    [advogado_id] => 3
    [observacoes] => 
    [status_id] => 2
    [retomado] => 0
)

ID do processo: 470
Status retomado: Não
Tem acordos: Sim
Status atual: 2
Novo status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-05-23
    [advogado_id] => 3
    [id] => 470
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 594), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 470
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso


2025-05-28 19:16:50 - Nova requisição
POST data: Array
(
    [id] => 431
    [nome] => 
    [cpf_cnpj] => 
    [pa_id] => 23
    [numero_contrato] => 
    [modalidade_id] => 
    [data_distribuicao] => 2025-05-28
    [data_envio] => 2025-05-28
    [advogado_id] => 2
    [observacoes] => 
    [status_id] => 2
    [retomado] => 0
)

ID do processo: 431
Status retomado: Não
Tem acordos: Sim
Status atual: 2
Novo status ID: 2
Parâmetros da atualização: Array
(
    [status_id] => 2
    [data_envio] => 2025-05-28
    [advogado_id] => 2
    [id] => 431
)

Processo deixou de ser RETOMADO, buscando último acordo para reativar
Status VIGENTE encontrado com ID: 1
Último acordo encontrado (ID: 703), reativando...
Acordo reativado com sucesso
Parâmetros do histórico: Array
(
    [processo_id] => 431
    [status_id] => 2
    [observacoes] => Processo atualizado para ACORDO JUDICIAL
)

Transação commitada com sucesso

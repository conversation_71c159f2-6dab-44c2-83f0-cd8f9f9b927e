<?php
// Iniciar sessão se ainda não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Definir cabeçalho para garantir que a resposta seja JSON
header('Content-Type: application/json');

// Iniciar captura de saída para evitar que HTML acidental seja enviado
ob_start();

try {
    // Usar caminhos absolutos para os includes
    try {
        require_once __DIR__ . '/../../auth_check.php';
    } catch (Exception $e) {
        // Se houver erro ao incluir auth_check.php, apenas continuamos
        // já que vamos fazer nossa própria verificação de autenticação
        error_log("Aviso: Erro ao incluir auth_check.php: " . $e->getMessage());
    }
    
    // Verificar se o database.php está no diretório correto
    if (file_exists(__DIR__ . '/../../config/database.php')) {
        require_once __DIR__ . '/../../config/database.php';
    } else {
        require_once __DIR__ . '/../config/database.php';
    }

    // Verificar se o usuário tem permissão de GESTOR (obtendo permissão diretamente do banco)
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('Usuário não autenticado.');
    }
    
    $stmt = $pdo->prepare("
        SELECT tipo_acesso 
        FROM cbp_permissoes_usuarios 
        WHERE usuario_id = ? 
        AND ativo = 1
        AND tipo_acesso = 'GESTOR'
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $permissao = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$permissao) {
        throw new Exception('Acesso não autorizado. Apenas gestores podem excluir advogados.');
    }

    // Log para depuração
    error_log("=== INÍCIO DO PROCESSO DE EXCLUSÃO DE ADVOGADO ===");
    error_log("Método: " . $_SERVER['REQUEST_METHOD']);
    error_log("Dados recebidos: " . print_r($_POST, true));

    // Verificar se a requisição é POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido.');
    }

    // Validar parâmetros obrigatórios
    if (!isset($_POST['advogado_id']) || !is_numeric($_POST['advogado_id'])) {
        throw new Exception('ID do advogado inválido.');
    }

    $advogado_id = (int)$_POST['advogado_id'];
    error_log("Advogado ID para exclusão: " . $advogado_id);

    // Iniciar transação
    $pdo->beginTransaction();

    // Verificar se o advogado existe
    $stmt = $pdo->prepare("SELECT * FROM cbp_advogados WHERE id = ?");
    $stmt->execute([$advogado_id]);
    $advogado = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$advogado) {
        throw new Exception('Advogado não encontrado.');
    }
    
    error_log("Dados do advogado encontrado para exclusão:");
    error_log(print_r($advogado, true));
    
    // Verificar se o advogado está sendo usado em honorários ou processos
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_honorarios WHERE advogado_id = ?");
    $stmt->execute([$advogado_id]);
    $honorarios_count = $stmt->fetchColumn();
    
    if ($honorarios_count > 0) {
        throw new Exception('Não é possível excluir este advogado porque ele está vinculado a ' . $honorarios_count . ' honorário(s).');
    }
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_processos_judiciais WHERE advogado_id = ?");
    $stmt->execute([$advogado_id]);
    $processos_count = $stmt->fetchColumn();
    
    if ($processos_count > 0) {
        throw new Exception('Não é possível excluir este advogado porque ele está vinculado a ' . $processos_count . ' processo(s) judicial(is).');
    }
    
    // Excluir o advogado
    $stmt = $pdo->prepare("DELETE FROM cbp_advogados WHERE id = ?");
    $stmt->execute([$advogado_id]);
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('Falha ao excluir o advogado.');
    }

    error_log("Advogado excluído com sucesso");

    // Registrar a operação no log do sistema
    $detalhes_log = "Exclusão de advogado - ID: {$advogado_id} - Nome: {$advogado['nome']} - OAB: {$advogado['oab']}";
    
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Exclusão de Advogado', $detalhes_log]);
    
    error_log("Registro de log criado para a exclusão do advogado");

    // Commit
    $pdo->commit();
    error_log("=== FIM DA EXCLUSÃO DE ADVOGADO - SUCESSO ===");

    // Limpar buffer de saída antes de enviar a resposta JSON
    ob_end_clean();
    
    // Responder com sucesso em formato JSON
    echo json_encode([
        'success' => true,
        'message' => 'Advogado excluído com sucesso.'
    ]);
    exit;

} catch (PDOException $e) {
    // Rollback em caso de erro com o banco de dados
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("=== ERRO NA EXCLUSÃO DE ADVOGADO (PDO) ===");
    error_log("Mensagem: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("=== FIM DO ERRO ===");

    // Limpar buffer de saída antes de enviar a resposta JSON
    ob_end_clean();
    
    // Responder com erro em formato JSON
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro de banco de dados: ' . $e->getMessage()
    ]);
    exit;

} catch (Exception $e) {
    // Rollback em caso de qualquer outro erro
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("=== ERRO NA EXCLUSÃO DE ADVOGADO ===");
    error_log("Mensagem: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("=== FIM DO ERRO ===");

    // Limpar buffer de saída antes de enviar a resposta JSON
    ob_end_clean();
    
    // Responder com erro em formato JSON
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    exit;
} 
<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

// Habilitar exibição de erros para debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json');

try {
    // Log para debug
    error_log('Iniciando excluir_ajuizamento.php');
    error_log('POST data: ' . print_r($_POST, true));

    // Validar método da requisição
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    // Validação do ID
    if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
        throw new Exception('ID inválido');
    }

    $id = $_POST['id'];
    error_log('ID do ajuizamento: ' . $id);

    // Inicia a transação
    $pdo->beginTransaction();

    // Atualiza os campos de ajuizamento para NULL
    $stmt = $pdo->prepare("
        UPDATE cbp_processos_judiciais 
        SET 
            numero_processo = NULL,
            data_ajuizamento = NULL,
            valor_ajuizado = NULL
        WHERE id = :id
    ");

    $stmt->execute(['id' => $id]);

    // Confirma a transação
    $pdo->commit();

    error_log('Ajuizamento removido com sucesso');

    echo json_encode([
        'success' => true,
        'message' => 'Ajuizamento removido com sucesso'
    ]);

} catch (Exception $e) {
    // Em caso de erro, desfaz a transação
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    error_log('Erro ao remover ajuizamento: ' . $e->getMessage());

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao remover ajuizamento: ' . $e->getMessage()
    ]);
} 
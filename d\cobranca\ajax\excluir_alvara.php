<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';
require_once '../verificar_acesso.php';

// Garantir que não temos saída antes dos cabeçalhos
ob_start();

// Definir cabeçalhos
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

try {
    // Verificar se é uma requisição POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    // Verificar se o usuário tem permissão de GESTOR
    if (TIPO_ACESSO_COBRANCA !== 'GESTOR') {
        throw new Exception('Acesso negado. Apenas gestores podem excluir alvarás.');
    }

    // Validar ID do alvará
    if (!isset($_POST['id']) || empty($_POST['id'])) {
        throw new Exception('ID do alvará não fornecido');
    }

    $id = (int)$_POST['id'];

    // Verificar se o alvará existe
    $stmt = $pdo->prepare("SELECT id FROM cbp_alvaras WHERE id = ?");
    $stmt->execute([$id]);
    if (!$stmt->fetch()) {
        throw new Exception('Alvará não encontrado');
    }

    // Iniciar transação
    $pdo->beginTransaction();

    // Buscar informações do alvará e processo antes de excluir
    $stmt = $pdo->prepare("
        SELECT a.*, p.numero_processo 
        FROM cbp_alvaras a 
        LEFT JOIN cbp_processos_judiciais p ON p.id = a.processo_id 
        WHERE a.id = ?
    ");
    $stmt->execute([$id]);
    $alvara = $stmt->fetch();

    if (!$alvara) {
        throw new Exception('Alvará não encontrado');
    }

    // Se o alvará tem um honorário associado, excluir o honorário
        $stmt = $pdo->prepare("
            SELECT h.id 
            FROM cbp_honorarios h
            INNER JOIN cbp_alvaras a ON h.processo_id = a.processo_id AND DATE(h.data_recebimento) = DATE(a.data_recebimento)
            WHERE a.id = ? AND h.tipo = 'ALVARA'
        ");
        $stmt->execute([$id]);
        $honorarios = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Excluir honorários encontrados
        if (!empty($honorarios)) {
            $placeholders = implode(',', array_fill(0, count($honorarios), '?'));
            $stmt = $pdo->prepare("DELETE FROM cbp_honorarios WHERE id IN ($placeholders)");
            $stmt->execute($honorarios);
    }

    // Excluir o alvará
    $stmt = $pdo->prepare("DELETE FROM cbp_alvaras WHERE id = ?");
    $stmt->execute([$id]);

    // Registrar no log
    $detalhes = "Exclusão de alvará - ID: " . $id . 
                " - Processo: " . $alvara['numero_processo'] . 
                " - Nº Alvará: " . $alvara['numero_alvara'] . 
                " - Data: " . date('d/m/Y', strtotime($alvara['data_alvara'])) . 
                " - Valor: R$ " . number_format($alvara['valor_alvara'], 2, ',', '.');
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Exclusão de Alvará', $detalhes]);

    // Commit da transação
    $pdo->commit();

    // Limpar qualquer saída de buffer antes de enviar a resposta JSON
    ob_clean();
    
    // Retornar sucesso
    echo json_encode([
        'success' => true,
        'message' => 'Alvará excluído com sucesso!'
    ]);
    exit;

} catch (Exception $e) {
    // Rollback em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    ob_clean();
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Erro: ' . $e->getMessage()
    ]);
    exit;
} catch (PDOException $e) {
    // Rollback em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    ob_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro no banco de dados: ' . $e->getMessage()
    ]);
    error_log($e->getMessage());
    exit;
}
?> 
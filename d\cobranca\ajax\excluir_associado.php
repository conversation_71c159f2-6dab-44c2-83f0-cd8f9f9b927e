<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';
require_once '../verificar_acesso.php';

// Habilitar log de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json');

try {
    // Log inicial
    error_log("Iniciando exclusão de associado");
    error_log("POST data: " . print_r($_POST, true));

    // Verificar se é uma requisição POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    // Verificar se o usuário tem permissão de GESTOR
    error_log("Verificando permissão - TIPO_ACESSO_COBRANCA: " . TIPO_ACESSO_COBRANCA);
    if (TIPO_ACESSO_COBRANCA !== 'GESTOR') {
        throw new Exception('Acesso negado. Apenas gestores podem excluir associados.');
    }

    // Validar ID do associado
    if (!isset($_POST['id']) || empty($_POST['id'])) {
        throw new Exception('ID do associado não fornecido');
    }

    $id = (int)$_POST['id'];
    error_log("ID do associado a ser excluído: " . $id);

    // Verificar se o associado existe
    $stmt = $pdo->prepare("SELECT id FROM cbp_associados WHERE id = ?");
    $stmt->execute([$id]);
    if (!$stmt->fetch()) {
        throw new Exception('Associado não encontrado');
    }
    error_log("Associado encontrado");

    // Verificar se o associado tem contratos vinculados
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_contratos WHERE associado_id = ?");
    $stmt->execute([$id]);
    $total_contratos = $stmt->fetchColumn();
    error_log("Total de contratos vinculados: " . $total_contratos);

    if ($total_contratos > 0) {
        throw new Exception('Não é possível excluir um associado que possui contratos vinculados');
    }

    $pdo->beginTransaction();
    error_log("Iniciando transação");

    // Buscar informações do associado antes de excluir
    $stmt = $pdo->prepare("SELECT a.*, pa.nome as pa_nome FROM cbp_associados a 
                          LEFT JOIN pontos_atendimento pa ON a.pa_id = pa.id 
                          WHERE a.id = ?");
    $stmt->execute([$id]);
    $associado = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$associado) {
        throw new Exception('Associado não encontrado');
    }
    error_log("Informações do associado recuperadas: " . print_r($associado, true));

    // Excluir o associado
    $stmt = $pdo->prepare("DELETE FROM cbp_associados WHERE id = ?");
    $stmt->execute([$id]);
    error_log("Associado excluído do banco de dados");

    // Registrar no log
    $detalhes = "Exclusão de associado - ID: " . $id . 
                " - Nome: " . $associado['nome'] . 
                " - CPF/CNPJ: " . $associado['cpf_cnpj'] . 
                " - PA: " . $associado['pa_nome'];
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Exclusão de Associado', $detalhes]);
    error_log("Log registrado");

    $pdo->commit();
    error_log("Transação commitada com sucesso");

    echo json_encode(['success' => true, 'message' => 'Associado excluído com sucesso!']);

} catch (Exception $e) {
    error_log("Erro ao excluir associado: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
        error_log("Transação revertida");
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    error_log("Erro de banco de dados ao excluir associado: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
        error_log("Transação revertida");
    }

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao excluir associado: ' . $e->getMessage()
    ]);
} 
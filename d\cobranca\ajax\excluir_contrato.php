<?php
// Desabilitar exibição de erros
error_reporting(0);
ini_set('display_errors', 0);

// Definir que a resposta será um JSON
header('Content-Type: application/json');

require_once '../../auth_check.php';
require_once '../../config/database.php';
require_once '../verificar_acesso.php';

try {
    // Verificar se é uma requisição POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    // Verificar se o usuário tem permissão de GESTOR
    if (TIPO_ACESSO_COBRANCA !== 'GESTOR') {
        throw new Exception('Acesso negado. Apenas gestores podem remover contratos vinculados.');
    }

    // Validar ID do contrato
    if (!isset($_POST['id']) || empty($_POST['id'])) {
        throw new Exception('ID do contrato não fornecido');
    }

    $id = (int)$_POST['id'];

    // Verificar se o contrato existe
    $stmt = $pdo->prepare("SELECT id FROM cbp_contratos WHERE id = ?");
    $stmt->execute([$id]);
    if (!$stmt->fetch()) {
        throw new Exception('Contrato não encontrado');
    }

    // Remover vínculo do contrato com o processo
    $stmt = $pdo->prepare("DELETE FROM cbp_processos_contratos WHERE contrato_id = ?");
    $stmt->execute([$id]);

    // Registrar no log
    $stmt = $pdo->prepare("SELECT c.*, a.nome as associado_nome 
                          FROM cbp_contratos c 
                          LEFT JOIN cbp_associados a ON c.associado_id = a.id 
                          WHERE c.id = ?");
    $stmt->execute([$id]);
    $contrato = $stmt->fetch();

    if (!$contrato) {
        throw new Exception('Contrato não encontrado');
    }

    // Excluir o contrato
    $stmt = $pdo->prepare("DELETE FROM cbp_contratos WHERE id = ?");
    $stmt->execute([$id]);

    // Registrar no log
    $detalhes = "Exclusão de contrato - ID: " . $id . 
                " - Nº Contrato: " . $contrato['numero_contrato'] . 
                " - Associado: " . $contrato['associado_nome'];
    
    // Adicionar valor ao log apenas se existir
    if (isset($contrato['valor_contratado']) && !empty($contrato['valor_contratado'])) {
        $detalhes .= " - Valor: R$ " . number_format($contrato['valor_contratado'], 2, ',', '.');
    }
    
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Exclusão de Contrato', $detalhes]);

    echo json_encode([
        'success' => true,
        'message' => 'Contrato excluído com sucesso!'
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 
<?php
// Iniciar captura de saída para evitar qualquer saída indesejada
ob_start();

require_once '../../auth_check.php';
require_once '../../config/database.php';

// Definir headers para garantir resposta JSON correta
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

try {
    // Verificar se o ID foi fornecido
    if (!isset($_POST['id'])) {
        throw new Exception('ID do processo não fornecido');
    }

    $processo_id = intval($_POST['id']);

    // Verificar se o processo existe e não tem ajuizamento
    $stmt = $pdo->prepare("
        SELECT id, data_ajuizamento 
        FROM cbp_processos_judiciais 
        WHERE id = ?
    ");
    $stmt->execute([$processo_id]);
    $processo = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$processo) {
        throw new Exception('Processo não encontrado');
    }

    if ($processo['data_ajuizamento'] !== null) {
        throw new Exception('Não é possível excluir um processo que já possui ajuizamento');
    }

    // Verificar se existem alvarás
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_alvaras WHERE processo_id = ?");
    $stmt->execute([$processo_id]);
    $tem_alvaras = $stmt->fetchColumn() > 0;

    if ($tem_alvaras) {
        throw new Exception('Não é possível excluir o processo pois existem alvarás cadastrados. Por favor, remova os alvarás primeiro.');
    }

    // Iniciar transação
    $pdo->beginTransaction();

    // Buscar informações do processo antes de excluir
    $stmt = $pdo->prepare("SELECT p.*, a.nome as advogado_nome 
                          FROM cbp_processos_judiciais p 
                          LEFT JOIN cbp_advogados a ON p.advogado_id = a.id 
                          WHERE p.id = ?");
    $stmt->execute([$processo_id]);
    $processo_info = $stmt->fetch();

    if (!$processo_info) {
        throw new Exception('Processo não encontrado');
    }

    // Remover contratos vinculados
    $stmt = $pdo->prepare("DELETE FROM cbp_processos_contratos WHERE processo_id = ?");
    $stmt->execute([$processo_id]);

    // Remover o processo
    $stmt = $pdo->prepare("DELETE FROM cbp_processos_judiciais WHERE id = ?");
    $stmt->execute([$processo_id]);

    // Registrar no log
    $detalhes = "Exclusão de processo - ID: " . $processo_id . 
                " - Nº Processo: " . $processo_info['numero_processo'] . 
                " - Valor: R$ " . number_format($processo_info['valor_causa'], 2, ',', '.') . 
                " - Vara: " . $processo_info['vara'] . 
                " - Comarca: " . $processo_info['comarca'] . 
                " - Advogado: " . $processo_info['advogado_nome'];
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Exclusão de Processo', $detalhes]);

    // Confirmar transação
    $pdo->commit();

    // Limpar qualquer saída em buffer
    ob_clean();

    echo json_encode([
        'success' => true,
        'message' => 'Processo removido com sucesso'
    ]);

} catch (Exception $e) {
    // Em caso de erro, reverter transação
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // Limpar qualquer saída em buffer
    ob_clean();

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?> 
<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

$processo_id = isset($_GET['processo_id']) ? intval($_GET['processo_id']) : 0;
$acordo_id = isset($_GET['acordo_id']) ? intval($_GET['acordo_id']) : 0;
$modo = isset($_GET['modo']) ? $_GET['modo'] : 'criar';
$inativar_vigente = isset($_GET['inativar_vigente']) ? intval($_GET['inativar_vigente']) : 0;

// Buscar todos os associados ativos para a opção de Assunção de Dívida
$stmt = $pdo->query("SELECT id, nome, cpf_cnpj FROM cbp_associados WHERE ativo = 1 ORDER BY nome");
$associados = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Se estiver editando, buscar informações do acordo
$acordo = null;
if ($modo === 'editar' && $acordo_id > 0) {
    $stmt = $pdo->prepare("
        SELECT a.*, 
               p.*,
               p.associado_nome as nome,
               p.associado_documento as cpf_cnpj,
               m.nome as modalidade_nome 
        FROM cbp_acordos a
        INNER JOIN cbp_processos_judiciais p ON a.processo_id = p.id
        LEFT JOIN cbp_modalidades_processo m ON p.modalidade_id = m.id
        WHERE a.id = ?
    ");
    $stmt->execute([$acordo_id]);
    $acordo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$acordo) {
        echo '<div class="alert alert-danger">Acordo não encontrado.</div>';
        exit;
    }
    
    $processo_id = $acordo['processo_id'];
} else {
    // Buscar informações do processo
    $stmt = $pdo->prepare("
        SELECT p.*,
               p.associado_nome as nome, 
               p.associado_documento as cpf_cnpj,
               m.nome as modalidade_nome 
        FROM cbp_processos_judiciais p
        LEFT JOIN cbp_modalidades_processo m ON p.modalidade_id = m.id
        WHERE p.id = ?
    ");
    $stmt->execute([$processo_id]);
    $processo = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$processo) {
        echo '<div class="alert alert-danger">Processo não encontrado.</div>';
        exit;
    }
}

// Se for inativar acordo vigente, buscar informações do acordo atual
if ($inativar_vigente) {
    $stmt = $pdo->prepare("
        SELECT a.* 
        FROM cbp_acordos a 
        INNER JOIN cbp_status_acordo sa ON a.status_id = sa.id 
        WHERE a.processo_id = ? 
        AND sa.nome = 'VIGENTE' 
        AND a.ativo = 1
    ");
    $stmt->execute([$processo_id]);
    $acordo_vigente = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>

<form id="formAcordo" class="needs-validation" novalidate>
    <input type="hidden" name="processo_id" value="<?php echo $processo_id; ?>">
    <input type="hidden" name="inativar_vigente" value="<?php echo $inativar_vigente; ?>">
    <input type="hidden" name="acao" value="criar_acordo">
    <?php if ($modo === 'editar'): ?>
        <input type="hidden" name="acordo_id" value="<?php echo $acordo_id; ?>">
    <?php endif; ?>
    
    <?php if ($inativar_vigente && isset($acordo_vigente)): ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        Ao salvar este novo acordo, o acordo atual <?php echo isset($acordo_vigente['numero_repactuacao']) ? '(Repactuação nº ' . $acordo_vigente['numero_repactuacao'] . ')' : ''; ?> será inativado automaticamente.
    </div>
    <?php endif; ?>
    
    <!-- Informações do Processo -->
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="mb-0">Informações do Processo</h5>
        </div>
        <div class="card-body">
            <table class="table table-borderless mb-0">
                <tbody>
                    <tr>
                        <td style="width: 140px;"><strong>Nome:</strong></td>
                        <td><?php echo $modo === 'editar' ? $acordo['nome'] : ($processo['nome'] ?? 'Não informado'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>CPF/CNPJ:</strong></td>
                        <td><?php echo $modo === 'editar' ? $acordo['cpf_cnpj'] : ($processo['cpf_cnpj'] ?? 'Não informado'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Nº Processo:</strong></td>
                        <td><?php echo $modo === 'editar' ? $acordo['numero_processo'] : ($processo['numero_processo'] ?? 'Não informado'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Valor Ajuizado:</strong></td>
                        <td>R$ <?php echo number_format($modo === 'editar' ? $acordo['valor_ajuizado'] : $processo['valor_ajuizado'], 2, ',', '.'); ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Informações do Acordo -->
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="mb-0">Informações do Acordo</h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">Data do Acordo</label>
                    <input type="date" name="data_acordo" class="form-control" required 
                           value="<?php echo $modo === 'editar' ? $acordo['data_acordo'] : ''; ?>">
                </div>
                <div class="col-md-6">
                    <label class="form-label">Valor do Acordo</label>
                    <input type="text" name="valor_acordo" class="form-control money" required
                           value="<?php echo $modo === 'editar' ? number_format($acordo['valor_acordo'], 2, ',', '.') : ''; ?>">
                </div>
                <div class="col-md-12 mt-2">
                    <div class="card">
                        <div class="card-header p-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="usar_entrada" name="usar_entrada"
                                       <?php echo ($modo === 'editar' && ($acordo['valor_entrada'] > 0 || $acordo['alvara_id'])) ? 'checked' : ''; ?>>
                                <label class="form-check-label fw-bold" for="usar_entrada">
                                    Utilizar valor de entrada
                                </label>
                            </div>
                        </div>
                        <div class="card-body p-3" id="opcoes_entrada" style="display: <?php echo ($modo === 'editar' && ($acordo['valor_entrada'] > 0 || $acordo['alvara_id'])) ? 'block' : 'none'; ?>;">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">Tipo de Entrada</label>
                                    <select class="form-select" id="tipo_entrada" name="tipo_entrada">
                                        <option value="valor" <?php echo ($modo === 'editar' && $acordo['valor_entrada'] > 0 && !$acordo['alvara_id']) ? 'selected' : ''; ?>>Valor</option>

                                    </select>
                                </div>
                                
                                <div class="col-md-6" id="campo_valor_entrada" style="display: <?php echo ($modo === 'editar' && ($acordo['valor_entrada'] > 0 || $acordo['alvara_id'])) ? 'block' : 'none'; ?>;">
                                    <label class="form-label">Valor da Entrada</label>
                                    <input type="text" name="valor_entrada" class="form-control money"
                                           value="<?php echo $modo === 'editar' ? number_format($acordo['valor_entrada'], 2, ',', '.') : ''; ?>">
                                </div>
                                
                                <div class="col-md-6" id="campo_alvara" style="display: <?php echo ($modo === 'editar' && $acordo['alvara_id']) ? 'block' : 'none'; ?>;">
                                    <label class="form-label">Selecionar Alvará</label>
                                    <select class="form-select" name="alvara_id">
                                        <option value="">Selecione um alvará</option>
                                        <?php
                                        // Buscar alvarás disponíveis
                                        $stmt = $pdo->prepare("
                                            SELECT * FROM cbp_alvaras 
                                            WHERE processo_id = ? 
                                              AND (id NOT IN (SELECT alvara_id FROM cbp_acordos WHERE alvara_id IS NOT NULL AND id != ?)
                                              OR id = ?)
                                            ORDER BY data_recebimento DESC
                                        ");
                                        $stmt->execute([$processo_id, $acordo_id, $acordo_id ?? 0]);
                                        $alvaras = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                        
                                        foreach ($alvaras as $alvara) {
                                            $selected = ($modo === 'editar' && $acordo['alvara_id'] == $alvara['id']) ? 'selected' : '';
                                            echo '<option value="' . $alvara['id'] . '" data-valor="' . $alvara['valor'] . '" ' . $selected . '>' . 
                                                  date('d/m/Y', strtotime($alvara['data_recebimento'])) . 
                                                  ' - R$ ' . number_format($alvara['valor'], 2, ',', '.') . 
                                                  '</option>';
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 mt-2">
                    <div class="card">
                        <div class="card-header p-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="usar_alvara_receber" name="usar_alvara_receber">
                                <label class="form-check-label fw-bold" for="usar_alvara_receber">
                                    Alvará a Receber
                                </label>
                            </div>
                        </div>
                        <div class="card-body p-3" id="opcoes_alvara_receber" style="display: none;">
                            <div class="row g-3">
                                <div class="col-md-12">
                                    <label class="form-label">Valor do Alvará</label>
                                    <input type="text" name="valor_alvara_receber" class="form-control money">
                                    <small class="form-text text-muted">Este valor será abatido do acordo para cálculo das parcelas.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Número de Repactuação</label>
                    <input type="text" name="numero_repactuacao" class="form-control" required
                           value="<?php echo $modo === 'editar' ? $acordo['numero_repactuacao'] : ''; ?>">
                </div>
                <div class="col-md-4">
                    <label class="form-label"><i class="fas fa-phone-alt me-1"></i> Contato</label>
                    <input type="text" name="numero_contato" class="form-control telefone"
                           value="<?php echo $modo === 'editar' ? $acordo['numero_contato'] : ''; ?>"
                           placeholder="(00) 00000-0000">
                </div>
                <div class="col-md-4">
                    <label class="form-label"><i class="fas fa-university me-1"></i> Conta Corrente</label>
                    <input type="text" name="conta_corrente" class="form-control"
                           value="<?php echo $modo === 'editar' ? $acordo['conta_corrente'] : ''; ?>"
                           placeholder="Digite a conta corrente">
                </div>
                <div class="col-md-12 mt-2">
                    <div class="card">
                        <div class="card-header p-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="assuncao_divida" name="assuncao_divida"
                                       <?php echo ($modo === 'editar' && !empty($acordo['assuntor_id'])) ? 'checked' : ''; ?>>
                                <label class="form-check-label fw-bold" for="assuncao_divida">
                                    Assunção de Dívida
                                </label>
                </div>
                        </div>
                        <div class="card-body p-3" id="opcoes_assuncao" style="display: <?php echo ($modo === 'editar' && !empty($acordo['assuntor_id'])) ? 'block' : 'none'; ?>;">
                            <div class="row g-3">
                                <div class="col-md-12">
                                    <label class="form-label">Selecionar Associado Assuntor</label>
                                    <select class="form-select select2" id="assuntor_id" name="assuntor_id">
                                        <option value="">Selecione um associado</option>
                                        <?php foreach ($associados as $associado): ?>
                                            <option value="<?php echo $associado['id']; ?>" 
                                                    <?php echo ($modo === 'editar' && isset($acordo['assuntor_id']) && $acordo['assuntor_id'] == $associado['id']) ? 'selected' : ''; ?>>
                                                <?php echo $associado['nome']; ?> - 
                                                <?php 
                                                    $doc = $associado['cpf_cnpj'];
                                                    if (strlen($doc) === 11) {
                                                        echo substr($doc, 0, 3) . '.' . substr($doc, 3, 3) . '.' . substr($doc, 6, 3) . '-' . substr($doc, 9, 2);
                                                    } else if (strlen($doc) === 14) {
                                                        echo substr($doc, 0, 2) . '.' . substr($doc, 2, 3) . '.' . substr($doc, 5, 3) . '/' . substr($doc, 8, 4) . '-' . substr($doc, 12, 2);
                                                    } else {
                                                        echo $doc;
                                                    }
                                                ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <label class="form-label">Observações</label>
                    <textarea name="observacoes" class="form-control" rows="3"><?php echo $modo === 'editar' ? $acordo['observacoes'] : ''; ?></textarea>
                </div>
                <div class="col-md-12 mt-3">
                    <div class="card">
                        <div class="card-header p-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="usar_honorario_personalizado" name="usar_honorario_personalizado" 
                                       <?php echo ($modo === 'editar' && !empty($acordo['usar_honorario_personalizado'])) ? 'checked' : ''; ?>>
                                <label class="form-check-label fw-bold" for="usar_honorario_personalizado">
                                    <i class="fas fa-percent me-1"></i> Usar Porcentagem de Honorário Personalizada
                                </label>
                            </div>
                        </div>
                        <div class="card-body p-3" id="opcoes_honorario" style="display: <?php echo ($modo === 'editar' && !empty($acordo['usar_honorario_personalizado'])) ? 'block' : 'none'; ?>;">
                            <div class="row g-3">
                                <div class="col-md-12">
                                    <?php
                                    // Buscar porcentagem padrão
                                    $stmt = $pdo->prepare("SELECT valor FROM cbp_configuracoes WHERE chave = 'porcentagem_padrao_honorarios'");
                                    $stmt->execute();
                                    $porcentagem_padrao = $stmt->fetchColumn();
                                    if (!$porcentagem_padrao) {
                                        $porcentagem_padrao = 13.0435; // Valor padrão caso não tenha sido configurado
                                    }
                                    ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        A porcentagem padrão atual é de <strong><?= number_format($porcentagem_padrao, 4, ',', '.') ?>%</strong>. 
                                        Defina uma porcentagem personalizada para este acordo caso seja diferente.
                                    </div>
                                    <label class="form-label">Porcentagem de Honorário Personalizada (%)</label>
                                    <input type="text" class="form-control porcentagem" id="porcentagem_honorario_personalizada" name="porcentagem_honorario_personalizada" 
                                           value="<?php echo $modo === 'editar' && isset($acordo['porcentagem_honorario_personalizada']) ? number_format($acordo['porcentagem_honorario_personalizada'], 4, ',', '.') : number_format($porcentagem_padrao, 4, ',', '.'); ?>">
                                    <small class="form-text text-muted">
                                        Esta porcentagem será aplicada para calcular os honorários de todas as parcelas, entradas e alvarás deste acordo.
                                        <strong>Você pode usar 0% para acordos sem honorários.</strong>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-3">
        <div class="card-header">
            <h6 class="mb-0">Configuração das Parcelas</h6>
        </div>
        <div class="card-body">
            <!-- Etapa 1: Tipo de Vencimento -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <label class="form-label">Tipo de Vencimento</label>
                    <select class="form-select" name="tipo_vencimento" id="tipo_vencimento" required>
                        <option value="">Selecione o tipo de vencimento</option>
                        <option value="mensal" <?php echo (!isset($acordo['tipo_vencimento']) || $acordo['tipo_vencimento'] === 'mensal') ? 'selected' : ''; ?>>Mensal</option>
                        
                        <option value="manual" <?php echo (isset($acordo['tipo_vencimento']) && $acordo['tipo_vencimento'] === 'manual') ? 'selected' : ''; ?>>Inserir Manualmente</option>
                    </select>
                </div>
            </div>

            <!-- Etapa 2: Quantidade e Vencimento -->
            <div id="secao_quantidade_vencimento" style="display: none;">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Quantidade de Parcelas</label>
                        <input type="number" name="quantidade_parcelas" id="quantidade_parcelas" class="form-control" min="1" required
                               value="<?php echo $modo === 'editar' ? $acordo['quantidade_parcelas'] : '1'; ?>">
                        <small class="form-text text-muted" id="texto_ajuda_parcelas">
                            Defina a quantidade de parcelas do acordo
                        </small>
                    </div>
                    <div class="col-md-6" id="container_vencimento" style="display: none;">
                        <!-- Vencimento Mensal -->
                        <div id="vencimento_mensal_container" style="display: none;">
                            <label class="form-label">Dia do Vencimento Mensal</label>
                            <input type="number" name="dia_vencimento" id="dia_vencimento" class="form-control" min="1" max="31"
                                   value="<?php echo $modo === 'editar' ? $acordo['dia_vencimento'] : ''; ?>">
                            <small class="form-text text-muted">
                                Para meses com menos dias, o vencimento será ajustado para o último dia
                            </small>
                            
                            <div class="mt-3">
                                <label class="form-label">Data de Vencimento Inicial</label>
                                <input type="date" name="data_vencimento_inicial" id="data_vencimento_inicial" class="form-control"
                                      value="<?php echo $modo === 'editar' && isset($acordo['data_vencimento_inicial']) ? $acordo['data_vencimento_inicial'] : ''; ?>">
                                <small class="form-text text-muted">
                                    A primeira data de vencimento. O dia deve ser igual ao Dia do Vencimento Mensal.
                                </small>
                            </div>
                        </div>
                        <!-- Vencimento Anual -->
                        <div id="vencimento_anual_container" style="display: none;">
                            <label class="form-label">Data de Vencimento Anual</label>
                            <input type="date" name="data_vencimento_anual" id="data_vencimento_anual" class="form-control"
                                   value="<?php echo $modo === 'editar' && isset($acordo['data_vencimento_anual']) ? $acordo['data_vencimento_anual'] : ''; ?>">
                            <small class="form-text text-muted">
                                Selecione qualquer dia do ano, apenas o dia e mês serão considerados
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Etapa 3: Cálculo das Parcelas -->
            <div id="secao_calculo_parcelas" style="display: none;">
                <!-- Opções de Cálculo (apenas para tipos não manuais) -->
                <div id="opcoes_calculo" style="display: none;">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">Tipo de Cálculo das Parcelas</label>
                            <div class="mt-2">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="tipo_calculo" id="tipo_calculo_automatico" value="automatico" 
                                           <?php echo (!isset($acordo['tipo_calculo']) || $acordo['tipo_calculo'] === 'automatico') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="tipo_calculo_automatico">Automático</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="tipo_calculo" id="tipo_calculo_manual" value="manual"
                                           <?php echo (isset($acordo['tipo_calculo']) && $acordo['tipo_calculo'] === 'manual') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="tipo_calculo_manual">Manual</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Valor Automático por Parcela -->
                    <div id="info_parcela_automatica" class="alert alert-info mb-3" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        Valor por parcela: <strong id="valor_parcela_info">R$ 0,00</strong>
                    </div>
                </div>

                <!-- Tabela para Parcelas Manuais (quando tipo for manual) -->
                <div id="parcelas_manuais_container" style="display: none;">
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        Informe o valor e a data de vencimento para cada parcela
                    </div>
                    <!-- Painel de Resumo dos Valores -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <p class="mb-1"><strong>Valor do Acordo:</strong></p>
                                    <p id="resumo_valor_acordo" class="h5 mb-3">R$ 0,00</p>
                                </div>
                                <div class="col-md-3">
                                    <p class="mb-1"><strong>Valor de Entrada:</strong></p>
                                    <p id="resumo_valor_entrada" class="h5 mb-3">R$ 0,00</p>
                                </div>
                                <div class="col-md-3">
                                    <p class="mb-1"><strong>Valor Restante:</strong></p>
                                    <p id="resumo_valor_restante" class="h5 mb-3">R$ 0,00</p>
                                </div>
                                <div class="col-md-3">
                                    <p class="mb-1"><strong>Total das Parcelas:</strong></p>
                                    <p id="resumo_total_parcelas" class="h5 mb-3">R$ 0,00</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div id="feedback_diferenca" class="alert alert-info mb-0" style="display: none;">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <span id="mensagem_diferenca"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row" id="container_validacao_valores" style="display: none;">
                                <div class="col-12">
                                    <div class="alert alert-danger mb-0">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <span id="mensagem_validacao_valores"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered" id="tabela_parcelas_manuais">
                            <thead>
                                <tr>
                                    <th class="text-center" style="width: 100px;">Nº Parcela</th>
                                    <th>Valor</th>
                                    <th>Data de Vencimento</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Linhas geradas via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Container para cálculo manual (Mensal/Anual) -->
                <div id="container_calculo_manual" class="mt-3" style="display: none;">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Defina o valor para cada parcela
                    </div>
                    <div id="feedback_calculo_manual" class="alert alert-info mb-3" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="mensagem_calculo_manual"></span>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Parcela</th>
                                    <th>Valor</th>
                                    <th>Data de Vencimento</th>
                                </tr>
                            </thead>
                            <tbody id="tbody_parcelas_calculo_manual">
                                <!-- Parcelas serão geradas dinamicamente aqui -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <input type="hidden" id="valor_total_restante">
            <input type="hidden" id="quantidade_parcelas_atual">
            <input type="hidden" name="parcelas_manuais_json" id="parcelas_manuais_json">
            <input type="hidden" name="parcelas_manuais" id="parcelas_manuais">
        </div>
    </div>

    <div class="d-flex justify-content-end gap-2">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
        <button type="submit" class="btn btn-primary"><?php echo $modo === 'editar' ? 'Atualizar Acordo' : 'Salvar Acordo'; ?></button>
    </div>
</form>

<!-- Incluir Moment.js para manipulação de datas -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

<script>
$(document).ready(function() {
    // Máscara para valores monetários
    $('.money').inputmask({
        alias: 'currency',
        radixPoint: ',',
        groupSeparator: '.',
        allowMinus: false,
        prefix: 'R$ ',
        digits: 2,
        digitsOptional: false,
        rightAlign: false,
        unmaskAsNumber: true
    });

    // Máscara para telefone
    $('.telefone').inputmask('(99) 99999-9999');
    
    // Máscara para porcentagem com 4 casas decimais
    $('.porcentagem').inputmask({
        alias: 'numeric',
        groupSeparator: '.',
        radixPoint: ',',
        autoGroup: true,
        digits: 4,
        digitsOptional: false,
        suffix: '%',
        rightAlign: false
    });

    // Inicializar select2 para o assuntor se o checkbox estiver marcado
    if ($('#assuncao_divida').is(':checked')) {
        $('#assuntor_id').select2({
            dropdownParent: $('#opcoes_assuncao'),
            placeholder: 'Selecione um associado',
            width: '100%'
        });
    }

    // Controle da exibição dos campos de honorário personalizado
    $('#usar_honorario_personalizado').change(function() {
        if ($(this).is(':checked')) {
            $('#opcoes_honorario').slideDown();
        } else {
            $('#opcoes_honorario').slideUp();
        }
    });

    // No início do document.ready, adicionar um código para remover o botão caso exista
    if ($('#tipo_vencimento').val() === 'manual') {
        $('#btn_distribuir_igualmente').remove();
    }

    // Fix específico para o cálculo manual - clique direto no rádio
    $('#tipo_calculo_manual').on('click', function() {
        console.log('Clique direto no radio de cálculo manual');
        const quantidade = parseInt($('#quantidade_parcelas').val()) || 0;
        const tipoVencimento = $('#tipo_vencimento').val();
        
        if (quantidade > 0 && tipoVencimento !== 'manual') {
            // Esconder opções automáticas
            $('#info_parcela_automatica').hide();
            
            // Remover campos antigos
            $('#tbody_parcelas_calculo_manual').empty();
            $('#container_valores_manuais').empty();
            
            // Mostrar container de cálculo manual
            $('#container_calculo_manual').show();
            
            // Gerar parcelas com um pequeno atraso
            setTimeout(function() {
                gerarParcelasCalculoManual();
                $('#feedback_calculo_manual').show();
                validarValoresParcelas();
            }, 100);
        }
    });

    // Controle da exibição dos campos de entrada
    $('#usar_entrada').change(function() {
        if ($(this).is(':checked')) {
            $('#opcoes_entrada').slideDown();
            // Garantir que o tipo de entrada padrão seja 'valor' e mostrar o campo
            $('#tipo_entrada').val('valor').trigger('change');
            $('#campo_valor_entrada').show();
            $('#campo_alvara').hide();
        } else {
            $('#opcoes_entrada').slideUp();
            // Limpar os campos quando desmarcar
            $('input[name="valor_entrada"]').val('');
            $('select[name="alvara_id"]').val('').trigger('change');
        }
        calcularValorParcela();
    });

    // Controle da exibição do campo de associado assuntor
    $('#assuncao_divida').change(function() {
        if ($(this).is(':checked')) {
            $('#opcoes_assuncao').slideDown();
            
            // Inicializa o select2 no selector de associados se ainda não foi inicializado
            if (!$('#assuntor_id').hasClass('select2-hidden-accessible')) {
                $('#assuntor_id').select2({
                    dropdownParent: $('#opcoes_assuncao'),
                    placeholder: 'Selecione um associado',
                    width: '100%'
                });
            }
        } else {
            $('#opcoes_assuncao').slideUp();
            $('#assuntor_id').val('').trigger('change');
        }
    });

    // Alternar entre entrada em valor ou alvará
    $('#tipo_entrada').change(function() {
        if ($(this).val() === 'valor') {
            $('#campo_valor_entrada').show();
            $('#campo_alvara').hide();
        } else if ($(this).val() === 'alvara') {
            $('#campo_valor_entrada').hide();
            $('#campo_alvara').show();
        } else {
            $('#campo_valor_entrada').show();
            $('#campo_alvara').show();
        }
        calcularValorParcela();
    });

    // Quando selecionar um alvará, atualizar o valor da entrada
    $('select[name="alvara_id"]').change(function() {
        calcularValorParcela();
    });

    // Controle do fluxo de configuração das parcelas
    $('#tipo_vencimento').on('change', function() {
        const tipoVencimento = $(this).val();
        console.log('Tipo de vencimento alterado para:', tipoVencimento);
        
        // Ocultar todos os containers de vencimento
        $('#vencimento_mensal_container, #vencimento_anual_container').hide();
        
        if (tipoVencimento === 'manual') {
            $('#tipo_calculo_manual').prop('checked', true);
            $('#tipo_calculo_automatico').prop('disabled', true);
            $('#container_vencimento').hide();
            atualizarJSONParcelasManuais();
            
            // Remover o botão distribuir igualmente, caso exista
            $('#btn_distribuir_igualmente').remove();
        } else {
            $('#tipo_calculo_automatico').prop('disabled', false);
            $('#container_vencimento').show();
            
            if (tipoVencimento === 'anual') {
                $('#vencimento_anual_container').show();
                $('#vencimento_mensal_container').hide();
                
                // Garantir que a data de vencimento anual esteja preenchida
                if (!$('#data_vencimento_anual').val()) {
                    // Definir para data atual mais um ano
                    const dataAtual = new Date();
                    dataAtual.setFullYear(dataAtual.getFullYear() + 1);
                    
                    const ano = dataAtual.getFullYear();
                    const mes = String(dataAtual.getMonth() + 1).padStart(2, '0');
                    const dia = String(dataAtual.getDate()).padStart(2, '0');
                    
                    const dataFormatada = `${ano}-${mes}-${dia}`;
                    $('#data_vencimento_anual').val(dataFormatada);
                    
                    console.log('Data de vencimento anual definida para:', dataFormatada);
                }
        } else {
                $('#vencimento_mensal_container').show();
                $('#vencimento_anual_container').hide();
                
                // Garantir que o dia de vencimento esteja preenchido
                if (!$('#dia_vencimento').val()) {
                    const dataAtual = new Date();
                    const dia = dataAtual.getDate();
                    $('#dia_vencimento').val(dia);
                    
                    console.log('Dia de vencimento definido para:', dia);
                }
            }
        }
        
        // Atualizar visibilidade das seções com base no tipo de vencimento
        atualizarOpcoesParcelas();
    });
    
    // Função para atualizar as opções de parcelas com base no tipo de vencimento e cálculo
    function atualizarOpcoesParcelas() {
        // Atualizar o resumo dos valores antes de processar as opções
        atualizarResumoValores();
        
        const tipoVencimento = $('#tipo_vencimento').val();
        const tipoCalculo = $('input[name="tipo_calculo"]:checked').val();
        const quantidade = parseInt($('#quantidade_parcelas').val()) || 0;
        
        console.log('Atualizando opções de parcelas - Tipo vencimento:', tipoVencimento, 'Tipo cálculo:', tipoCalculo, 'Quantidade:', quantidade);
        
        if (quantidade <= 0) {
            $('#secao_calculo_parcelas').slideUp();
            $('#container_calculo_manual, #info_parcela_automatica, #parcelas_manuais_container').hide();
            $('#feedback_diferenca, #feedback_calculo_manual').hide();
            return;
        }
        
            $('#secao_calculo_parcelas').slideDown();
            
            if (tipoVencimento === 'manual') {
                $('#opcoes_calculo').hide();
                $('#parcelas_manuais_container').show();
            $('#container_calculo_manual, #info_parcela_automatica').hide();
                gerarParcelasManual();
            
            // Remover o botão de distribuir igualmente se existir
            $('#btn_distribuir_igualmente').remove();
            } else {
                $('#opcoes_calculo').show();
            $('#parcelas_manuais_container').hide();
                
                if (tipoCalculo === 'manual') {
                    $('#container_calculo_manual').show();
                    $('#info_parcela_automatica').hide();
                    setTimeout(function() {
                        gerarParcelasCalculoManual();
                    }, 100);
                    $('#feedback_diferenca, #feedback_calculo_manual').show();
                
                // Adicionar botão de distribuir igualmente para cálculo manual
                if (!$('#btn_distribuir_igualmente_calculo').length) {
                    $('#container_calculo_manual .card-header').append(`
                        <button type="button" id="btn_distribuir_igualmente_calculo" class="btn btn-sm btn-outline-primary float-right">
                            <i class="fas fa-balance-scale mr-1"></i> Distribuir valor igualmente
                        </button>
                    `);
                    
                    // Aplicar evento ao botão
                    $('#btn_distribuir_igualmente_calculo').on('click', function() {
                        const { valorRestante } = atualizarResumoValores();
                        const quantidade = parseInt($('#quantidade_parcelas').val()) || 0;
                        
                        if (quantidade <= 0) {
                            Swal.fire('Atenção', 'Informe a quantidade de parcelas primeiro.', 'warning');
                            return;
                        }
                        
                        const valorParcela = (valorRestante / quantidade).toFixed(2);
                        
                        console.log('Distribuindo valor (cálculo manual):', valorRestante, 'em', quantidade, 'parcelas de', valorParcela);
                        
                        // Para cálculo manual com vencimento automático
                        $('.parcela-manual').each(function() {
                            $(this).val(valorParcela).trigger('input');
                        });
                        
                        // Forçar validação
                        setTimeout(function() {
                            validarValoresParcelas();
                        }, 300);
                    });
                }
            } else {
                $('#container_calculo_manual').hide();
                $('#info_parcela_automatica').show();
                $('#feedback_diferenca, #feedback_calculo_manual').hide();
                calcularValorParcela();
            }
        }
    }
    
    // Monitorar alterações na quantidade de parcelas
    $('#quantidade_parcelas').on('change', function() {
        atualizarOpcoesParcelas();
    });
    
    // Monitorar alterações no tipo de cálculo
    $('input[name="tipo_calculo"]').on('change', function() {
        atualizarOpcoesParcelas();
    });

    // Função para calcular valor das parcelas
    function calcularValorParcela() {
        const valorAcordo = parseFloat($('input[name="valor_acordo"]').inputmask('unmaskedvalue') || 0);
        let valorEntrada = 0;
        let valorAlvaraReceber = 0;
        
        if ($('#usar_entrada').is(':checked')) {
            if ($('#tipo_entrada').val() === 'valor' || $('#tipo_entrada').val() === 'misto') {
                valorEntrada += parseFloat($('input[name="valor_entrada"]').inputmask('unmaskedvalue') || 0);
            }
            
            if ($('#tipo_entrada').val() === 'alvara' || $('#tipo_entrada').val() === 'misto') {
                const alvaraOption = $('select[name="alvara_id"] option:selected');
                if (alvaraOption.val()) {
                    valorEntrada += parseFloat(alvaraOption.data('valor')) || 0;
                }
            }
        }
        
        // Considerar valor do alvará a receber
        if ($('#usar_alvara_receber').is(':checked')) {
            valorAlvaraReceber = parseFloat($('input[name="valor_alvara_receber"]').inputmask('unmaskedvalue') || 0);
        }
        
        const valorRestante = Math.max(0, valorAcordo - valorEntrada - valorAlvaraReceber);
        const quantidadeParcelas = parseInt($('#quantidade_parcelas').val()) || 1;
        const valorParcela = valorRestante / quantidadeParcelas;
        
        $('#valor_parcela_info').text(valorParcela.toLocaleString('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }));
        
        return {
            valorAcordo,
            valorEntrada,
            valorAlvaraReceber,
            valorRestante,
            valorParcela
        };
    }

    // Função para gerar campos de valores manuais
    function gerarCamposValoresManuais() {
        const quantidade = parseInt($('#quantidade_parcelas').val()) || 1;
        const valorAcordo = parseFloat($('input[name="valor_acordo"]').inputmask('unmaskedvalue') || 0);
        let valorEntrada = 0;
        
        if ($('#usar_entrada').is(':checked')) {
            if ($('#tipo_entrada').val() === 'valor' || $('#tipo_entrada').val() === 'misto') {
                valorEntrada += parseFloat($('input[name="valor_entrada"]').inputmask('unmaskedvalue') || 0);
            }
            
            if ($('#tipo_entrada').val() === 'alvara' || $('#tipo_entrada').val() === 'misto') {
                const alvaraOption = $('select[name="alvara_id"] option:selected');
                if (alvaraOption.val()) {
                    valorEntrada += parseFloat(alvaraOption.data('valor')) || 0;
                }
            }
        }
        
        const valorRestante = Math.max(0, valorAcordo - valorEntrada);
        const valorParcela = valorRestante / quantidade;
        
        const container = $('#container_valores_manuais');
        container.empty();
        
        for (let i = 1; i <= quantidade; i++) {
            container.append(`
                <div class="col-md-4 mb-3">
                    <label class="form-label">Parcela ${i}</label>
                    <div class="input-group">
                        <span class="input-group-text">R$</span>
                        <input type="text" 
                               name="valor_parcela_manual[${i}]" 
                               class="form-control money parcela-manual" 
                               value="${valorParcela.toLocaleString('pt-BR', {minimumFractionDigits: 2})}" 
                               required>
                    </div>
                </div>
            `);
        }

        // Reinicializar máscaras monetárias
        $('.parcela-manual').inputmask({
            alias: 'currency',
            radixPoint: ',',
            groupSeparator: '.',
            allowMinus: false,
            prefix: 'R$ ',
            digits: 2,
            digitsOptional: false,
            rightAlign: false,
            unmaskAsNumber: true
        });

        // Atualizar JSON quando valores forem alterados
        $('.parcela-manual').on('change', function() {
            atualizarJSONParcelasManuais();
        });
    }

    // Função para gerar parcelas manuais
    function gerarParcelasManual() {
        const quantidade = parseInt($('#quantidade_parcelas').val()) || 0;
        const tbody = $('#tabela_parcelas_manuais tbody');
        tbody.empty();
        
        console.log('Gerando parcelas manuais:', quantidade);
        
        if (quantidade > 0) {
            const { valorRestante } = atualizarResumoValores();
            const valorParcela = valorRestante / quantidade;
            
            for (let i = 1; i <= quantidade; i++) {
                const tr = $('<tr>').addClass('parcela-manual-row');
                
                // Coluna do número da parcela
                tr.append(`
                    <td class="text-center">
                        ${i}ª
                        <input type="hidden" name="numero_parcela_${i}" value="${i}">
                    </td>
                `);
                
                // Coluna do valor
                tr.append(`
                    <td>
                        <input type="text" 
                               class="form-control money2" 
                               name="valor_parcela_${i}"
                               data-parcela="${i}"
                               placeholder="0,00"
                               value="${valorParcela.toLocaleString('pt-BR', {
                                   minimumFractionDigits: 2,
                                   maximumFractionDigits: 2
                               })}">
                    </td>
                `);
                
                // Coluna da data de vencimento
                tr.append(`
                    <td>
                        <input type="text" 
                               class="form-control data-vencimento-manual" 
                               name="data_vencimento_${i}" 
                               placeholder="DD/MM/AAAA"
                               autocomplete="off"
                               required>
                    </td>
                `);
                
                tbody.append(tr);
            }
            
            // Atualizar cabeçalho - adicionar apenas o título sem o botão
            const container = $('#parcelas_manuais_container');
            container.find('.card-header').remove(); // Remover cabeçalho existente
            container.prepend(`
                <div class="card-header bg-light">
                    <h5 class="m-0 text-primary">
                        <i class="fas fa-list-ol"></i> Detalhamento das Parcelas
                    </h5>
                </div>
            `);
            
            // Remover botão caso exista
            $('#btn_distribuir_igualmente').remove();
            
            // Reinicializar máscaras monetárias
            tbody.find('.money2').inputmask('currency', {
                prefix: 'R$ ',
                groupSeparator: '.',
                radixPoint: ',',
                allowMinus: false,
                digits: 2,
                digitsOptional: false,
                rightAlign: false,
                unmaskAsNumber: true
            });
            
            // Aplicar máscara de data
            tbody.find('.data-vencimento-manual').inputmask('99/99/9999', {
                placeholder: 'DD/MM/AAAA',
                clearMaskOnLostFocus: false,
                oncomplete: function() {
                    $(this).trigger('change');
                }
            });
            
            // Exibir o container e o feedback
            $('#parcelas_manuais_container').show();
            $('#feedback_diferenca').show();
            
            // Atualizar validação inicial
            validarValoresParcelas();
            
            // Atualizar o JSON de parcelas manuais inicialmente
            setTimeout(function() {
                atualizarJSONParcelasManuais();
            }, 100);
        } else {
            $('#parcelas_manuais_container').hide();
        }
    }

    // Função para gerar parcelas para cálculo manual (Mensal/Anual)
    function gerarParcelasCalculoManual() {
        const quantidade = parseInt($('#quantidade_parcelas').val()) || 0;
        const tipoVencimento = $('#tipo_vencimento').val();
        const tbody = $('#tbody_parcelas_calculo_manual');
        tbody.empty();

        console.log('Gerando parcelas para cálculo manual - Quantidade:', quantidade);
        
            const { valorRestante } = atualizarResumoValores();
        const usarEntrada = $('#usar_entrada').is(':checked');
        const tipoEntrada = $('#tipo_entrada').val();
        
        console.log('Detalhes do cálculo manual:');
        console.log('- Tipo de entrada:', usarEntrada ? tipoEntrada : 'Sem entrada');
        if (usarEntrada) {
            console.log('- Valor monetário da entrada:', 
                tipoEntrada === 'valor' || tipoEntrada === 'misto' 
                ? parseFloat($('input[name="valor_entrada"]').inputmask('unmaskedvalue') || 0)
                : 0);
                
            if (tipoEntrada === 'alvara' || tipoEntrada === 'misto') {
                const alvaraOption = $('select[name="alvara_id"] option:selected');
                console.log('- Alvará selecionado:', alvaraOption.val() ? 'ID: ' + alvaraOption.val() : 'Nenhum');
                console.log('- Valor do alvará:', alvaraOption.val() ? parseFloat(alvaraOption.data('valor')) : 0);
            }
        }
        console.log('- Valor restante para dividir:', valorRestante);
        
        const valorParcela = valorRestante / quantidade;
        
        // Configurações de data de vencimento com base no tipo
        let dataBase = new Date();
        
        if (tipoVencimento === 'anual') {
            // Usar a data de vencimento anual especificada
                const dataAnual = $('#data_vencimento_anual').val();
            console.log('Data de vencimento anual:', dataAnual);
                
            if (dataAnual) {
                const [ano, mes, dia] = dataAnual.split('-').map(Number);
                dataBase = new Date(ano, mes - 1, dia);
                } else {
                // Definir para data atual + 1 ano
                dataBase.setFullYear(dataBase.getFullYear() + 1);
            }
        } else {
            // Para vencimento mensal, verificar se tem data inicial de vencimento
            const dataInicial = $('#data_vencimento_inicial').val();
            const diaVencimento = parseInt($('#dia_vencimento').val()) || dataBase.getDate();
            
            if (dataInicial) {
                // Usar a data inicial informada
                const [ano, mes, dia] = dataInicial.split('-').map(Number);
                
                // Verificar se o dia da data inicial é igual ao dia de vencimento
                if (dia === diaVencimento) {
                    dataBase = new Date(ano, mes - 1, dia);
                } else {
                    // Ajustar para o dia de vencimento no mesmo mês e ano
                    dataBase = new Date(ano, mes - 1, 1);
                    
                    // Ajustar para o dia de vencimento (ou último dia do mês se o dia for inválido)
                    const ultimoDia = new Date(ano, mes, 0).getDate();
                    dataBase.setDate(Math.min(diaVencimento, ultimoDia));
                }
            } else {
                // Comportamento anterior (próximo mês com o dia especificado)
                const mesAtual = dataBase.getMonth();
                const anoAtual = dataBase.getFullYear();
                
                // Avançar para o próximo mês
                dataBase = new Date(anoAtual, mesAtual + 1, 1);
                
                // Ajustar para o dia de vencimento (ou último dia do mês se o dia for inválido)
                const ultimoDia = new Date(dataBase.getFullYear(), dataBase.getMonth() + 1, 0).getDate();
                dataBase.setDate(Math.min(diaVencimento, ultimoDia));
            }
        }
        
        console.log('Data base para cálculos:', dataBase);
        
        // Gerar linhas com parcelas
            for (let i = 1; i <= quantidade; i++) {
            const dataVencimento = new Date(dataBase);
            let dataVencimentoFormatada = '';
            
            if (i > 1) {
                if (tipoVencimento === 'anual') {
                    // Para vencimento anual, adicionar anos
                    dataVencimento.setFullYear(dataVencimento.getFullYear() + (i - 1));
                    } else {
                    // Para vencimento mensal, adicionar meses
                    dataVencimento.setMonth(dataVencimento.getMonth() + (i - 1));
                    
                    // Ajustar para o último dia do mês se for um dia inválido
                    const ultimoDia = new Date(dataVencimento.getFullYear(), dataVencimento.getMonth() + 1, 0).getDate();
                    if (dataVencimento.getDate() > ultimoDia) {
                        dataVencimento.setDate(ultimoDia);
                    }
                }
            }
            
            // Formatar a data para exibição
            dataVencimentoFormatada = formatarData(dataVencimento);

                const tr = $('<tr>');
            tr.append(`<td class="text-center">${i}ª</td>`);
                tr.append(`
                    <td>
                        <input type="text" 
                               class="form-control parcela-manual money2" 
                               data-parcela="${i}"
                               placeholder="0,00" 
                               value="${valorParcela.toLocaleString('pt-BR', {
                                   minimumFractionDigits: 2,
                                   maximumFractionDigits: 2
                               })}">
                    </td>
                `);
                tr.append(`
                    <td>
                        <input type="text" 
                               class="form-control" 
                               value="${dataVencimentoFormatada || ''}" 
                               readonly>
                    </td>
                `);
                tbody.append(tr);
            }

            // Função auxiliar para formatar datas
            function formatarData(data) {
                if (!data) return '';
                const dia = String(data.getDate()).padStart(2, '0');
                const mes = String(data.getMonth() + 1).padStart(2, '0');
                const ano = data.getFullYear();
                return `${dia}/${mes}/${ano}`;
            }

            // Reinicializar máscaras monetárias
            $('.money2').inputmask('currency', {
                prefix: 'R$ ',
                groupSeparator: '.',
                radixPoint: ',',
                allowMinus: false,
                digits: 2,
                digitsOptional: false,
                rightAlign: false,
                unmaskAsNumber: true
            });

            // Mostrar o container e o feedback
            $('#container_calculo_manual').show();
            $('#feedback_calculo_manual').show();
            
            // Atualizar validação inicial
            validarValoresParcelas();
    }

    // Função para atualizar o resumo dos valores
    function atualizarResumoValores() {
        console.log('Valor do acordo (string):', $('input[name="valor_acordo"]').val());
        const valorAcordo = parseFloat($('input[name="valor_acordo"]').inputmask('unmaskedvalue') || 0);
        console.log('Valor do acordo (numero):', valorAcordo);
        
        let valorEntrada = 0;
        
        // Calcular valor da entrada
        if ($('#usar_entrada').is(':checked')) {
            // Entrada monetária
            if ($('#tipo_entrada').val() === 'valor' || $('#tipo_entrada').val() === 'misto') {
                const entradaMonetaria = parseFloat($('input[name="valor_entrada"]').inputmask('unmaskedvalue') || 0);
                console.log('Valor de entrada monetária:', entradaMonetaria);
                valorEntrada += entradaMonetaria;
            }
            
            // Entrada com alvará
            if ($('#tipo_entrada').val() === 'alvara' || $('#tipo_entrada').val() === 'misto') {
                const alvaraOption = $('select[name="alvara_id"] option:selected');
                if (alvaraOption.val()) {
                    const valorAlvara = parseFloat(alvaraOption.data('valor')) || 0;
                    console.log('Valor do alvará selecionado:', valorAlvara);
                    valorEntrada += valorAlvara;
                }
            }
        }
        
        console.log('Valor total da entrada (incluindo alvará):', valorEntrada);
        
        // Calcular valor restante
        const valorRestante = valorAcordo - valorEntrada;
        console.log('Valor restante calculado:', valorRestante);
        
        // Atualizar campos de exibição
        $('#resumo_valor_acordo').text(valorAcordo.toLocaleString('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }));
        
        $('#resumo_valor_entrada').text(valorEntrada.toLocaleString('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }));
        
        $('#resumo_valor_restante').text(valorRestante.toLocaleString('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }));

        return {
            valorAcordo,
            valorEntrada,
            valorRestante
        };
    }

    // Função para validar valores das parcelas
    function validarValoresParcelas() {
        console.log('Iniciando validação de valores das parcelas...');
        const { valorRestante } = atualizarResumoValores();
        let somaValores = 0;
        let hasParcelas = false;
        
        console.log('Valor restante do acordo:', valorRestante);
        
        // Calcular soma dos valores das parcelas
        if ($('#tipo_vencimento').val() === 'manual') {
            // Para vencimento manual, usar as linhas da tabela
            $('#tabela_parcelas_manuais tbody tr').each(function() {
                const valor = $(this).find('input.money2').inputmask('unmaskedvalue');
                if (valor) {
                    const valorParcela = parseFloat(valor);
                    console.log('Parcela encontrada:', valorParcela);
                    somaValores += valorParcela;
                hasParcelas = true;
                }
            });
        } else if ($('input[name="tipo_calculo"]:checked').val() === 'manual') {
            // Para cálculo manual com vencimento automático
            $('.parcela-manual').each(function() {
                const valor = $(this).inputmask('unmaskedvalue');
                if (valor) {
                    const valorParcela = parseFloat(valor);
                    console.log('Parcela encontrada:', valorParcela);
                    somaValores += valorParcela;
                hasParcelas = true;
                }
            });
        }
        
        console.log('Soma dos valores das parcelas:', somaValores);

        // Atualizar total das parcelas no resumo
        $('#resumo_total_parcelas').text(somaValores.toLocaleString('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }));

        // Se não houver parcelas ainda, não mostrar feedback
        if (!hasParcelas) {
            $('#feedback_diferenca, #feedback_calculo_manual').hide();
            return true;
        }
        
            const feedbackDiferenca = $('#feedback_diferenca');
            const mensagemDiferenca = $('#mensagem_diferenca');
            const feedbackCalculoManual = $('#feedback_calculo_manual');
            const mensagemCalculoManual = $('#mensagem_calculo_manual');

        // Calcular diferença
        const diferenca = somaValores - valorRestante;
        console.log('Diferença calculada:', diferenca);
        
        // Mostrar feedbacks
        $('#feedback_diferenca, #feedback_calculo_manual').show();
        
        // Validar se a soma está correta (com margem de erro de 0.01 para arredondamentos)
        if (Math.abs(diferenca) > 0.01) {
            if (diferenca < 0) {
                    // Configurar feedback principal
                feedbackDiferenca.removeClass('alert-success alert-info').addClass('alert-danger');
                    mensagemDiferenca.html(`
                    O valor total das parcelas está <strong>${Math.abs(diferenca).toLocaleString('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                    })}</strong> abaixo do valor restante
                    `);
                    
                    // Configurar feedback no container de cálculo manual
                feedbackCalculoManual.removeClass('alert-success alert-info').addClass('alert-danger');
                    mensagemCalculoManual.html(`
                    O valor total das parcelas está <strong>${Math.abs(diferenca).toLocaleString('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                    })}</strong> abaixo do valor restante
                    `);
                } else {
                    // Configurar feedback principal
                    feedbackDiferenca.removeClass('alert-info alert-success').addClass('alert-danger');
                    mensagemDiferenca.html(`
                        O valor total das parcelas está <strong>${Math.abs(diferenca).toLocaleString('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                        })}</strong> acima do valor restante
                    `);
                    
                    // Configurar feedback no container de cálculo manual
                    feedbackCalculoManual.removeClass('alert-info alert-success').addClass('alert-danger');
                    mensagemCalculoManual.html(`
                        O valor total das parcelas está <strong>${Math.abs(diferenca).toLocaleString('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                        })}</strong> acima do valor restante
                    `);
                }
                return false;
            } else {
                // Configurar feedback principal
                feedbackDiferenca.removeClass('alert-danger alert-info').addClass('alert-success');
                mensagemDiferenca.html('Os valores das parcelas estão corretos!');
                
                // Configurar feedback no container de cálculo manual
                feedbackCalculoManual.removeClass('alert-danger alert-info').addClass('alert-success');
                mensagemCalculoManual.html('Os valores das parcelas estão corretos!');
                return true;
            }
    }

    // Função para converter data para formato ISO
    function converterDataParaISO(dataStr) {
        if (!dataStr) return '';
        
        // Se já estiver no formato ISO (YYYY-MM-DD), retornar como está
        if (/^\d{4}-\d{2}-\d{2}$/.test(dataStr)) {
            return dataStr;
        }
        
        // Converter do formato DD/MM/YYYY para YYYY-MM-DD
        const partes = dataStr.split('/');
        if (partes.length === 3) {
            return `${partes[2]}-${partes[1]}-${partes[0]}`;
        }
        
        return dataStr;
    }

    // Função para atualizar o JSON de parcelas manuais
    function atualizarJSONParcelasManuais() {
        const parcelas = [];
        let todasPreenchidas = true;
        
        console.log('Iniciando captura de parcelas manuais...');
        
        // Iterar sobre as linhas da tabela, não sobre os inputs
        $('#tabela_parcelas_manuais tbody tr').each(function(index) {
            const row = $(this);
            console.log('Processando linha:', index + 1);
            
            // Capturar os valores da linha atual
            const numeroParcela = index + 1;
            const valorInput = row.find('input.money2');
            const dataInput = row.find('input.data-vencimento-manual');
            
            const valorStr = valorInput.inputmask('unmaskedvalue');
            const dataVencimento = dataInput.val();
            
            console.log('Dados capturados:', {
                numero: numeroParcela,
                valor: valorStr,
                data: dataVencimento,
                valorInput: valorInput.length ? 'encontrado' : 'não encontrado',
                dataInput: dataInput.length ? 'encontrado' : 'não encontrado'
            });
            
            // Validar se os campos necessários estão preenchidos
            if (!valorStr || !dataVencimento) {
                console.log('Campos incompletos detectados na linha', numeroParcela);
                todasPreenchidas = false;
                return false; // break the loop
            }
            
            // Converter e validar o valor
            const valor = parseFloat(valorStr);
            if (isNaN(valor) || valor <= 0) {
                console.log('Valor inválido detectado:', valorStr);
                todasPreenchidas = false;
                return false;
            }
            
            // Converter a data do formato DD/MM/YYYY para YYYY-MM-DD para o backend
            let dataISO = dataVencimento;
            if (/^\d{2}\/\d{2}\/\d{4}$/.test(dataVencimento)) {
                const partes = dataVencimento.split('/');
                dataISO = `${partes[2]}-${partes[1]}-${partes[0]}`;
                console.log(`Data convertida: ${dataVencimento} -> ${dataISO}`);
            }
            
            parcelas.push({
                numero: numeroParcela,
                        valor: valor,
                data_vencimento: dataISO
            });
        });
        
        if (!todasPreenchidas) {
            console.log('Algumas parcelas estão incompletas');
            return [];
        }
        
        // Ordenar parcelas por número
        parcelas.sort((a, b) => a.numero - b.numero);
        
        console.log('Parcelas capturadas com sucesso:', parcelas);
        
        // Atualizar campo hidden com o JSON
        const jsonStr = JSON.stringify(parcelas);
        $('#parcelas_manuais_json').val(jsonStr);
        console.log('JSON gerado:', jsonStr);
        
        return parcelas;
    }

    // Função para validar as parcelas manuais
    function validarParcelasManuais() {
        let valido = true;
        let totalParcelas = 0;
        
        $('#tabela_parcelas_manuais tbody tr').each(function() {
            const $row = $(this);
            const data = $row.find('.data_parcela').val();
            const valor = $row.find('.valor_parcela').inputmask('unmaskedvalue');
            
            if (!data || !valor) {
                valido = false;
                $row.addClass('erro');
                } else {
                $row.removeClass('erro');
                totalParcelas += parseFloat(valor);
            }
        });
        
        // Validar se o total das parcelas corresponde ao valor do acordo
        const valorAcordo = parseFloat($('#valor_acordo').inputmask('unmaskedvalue'));
        const valorEntrada = $('#usar_entrada').is(':checked') ? parseFloat($('#valor_entrada').inputmask('unmaskedvalue')) : 0;
        const valorEsperado = valorAcordo - valorEntrada;
        
        if (Math.abs(totalParcelas - valorEsperado) > 0.01) {
            alert('O valor total das parcelas deve ser igual ao valor do acordo menos a entrada.');
            valido = false;
        }
        
        return valido;
    }

    // Monitorar alterações em qualquer input no contêiner de parcelas manuais
    let timeoutId;
    $(document).on('input', '.money2', function() {
        console.log('Alteração detectada no campo:', $(this).attr('class'));
        clearTimeout(timeoutId);
        timeoutId = setTimeout(function() {
            console.log('Executando validação após alteração...');
            validarValoresParcelas();
            // Se estiver no modo de cálculo manual, atualizar o JSON
            if ($('#tipo_calculo_manual').is(':checked') || $('#tipo_vencimento').val() === 'manual') {
                atualizarJSONParcelasManuais();
            }
        }, 300);
    });

    // Função que inicializa o formulário com base no estado atual
    function inicializarFormulario() {
        // Disparar evento de alteração no tipo de vencimento para configurar os campos inicialmente
        $('#tipo_vencimento').trigger('change');
        
        // Se já tiver quantidade de parcelas definida, mostrar a seção de cálculo
        setTimeout(function() {
            if ($('#quantidade_parcelas').val() > 0) {
                const tipoCalculo = $('input[name="tipo_calculo"]:checked').val();
                const tipoVencimento = $('#tipo_vencimento').val();
                
                $('#secao_quantidade_vencimento').show();
                $('#secao_calculo_parcelas').show();
                
                if (tipoVencimento === 'mensal' || tipoVencimento === 'anual') {
                    $('#container_vencimento').show();
                    if (tipoVencimento === 'mensal') {
                        $('#vencimento_mensal_container').show();
                    } else {
                        $('#vencimento_anual_container').show();
                    }
                    
                    $('#opcoes_calculo').show();
                    
                    if (tipoCalculo === 'manual') {
                        $('#container_calculo_manual').show();
                        gerarParcelasCalculoManual();
                        $('#feedback_calculo_manual').show();
                        validarValoresParcelas();
                    } else {
                        $('#info_parcela_automatica').show();
                        calcularValorParcela();
                    }
                } else if (tipoVencimento === 'manual') {
                    $('#parcelas_manuais_container').show();
                    gerarParcelasManual();
                }
            }
            
            // Inicializar a validação
            atualizarResumoValores();
            validarValoresParcelas();
        }, 300);
    }

    // Chamar a inicialização quando o documento estiver pronto
    inicializarFormulario();

    // Submissão do formulário
    $('#formAcordo').on('submit', function(e) {
                e.preventDefault();
        
        const form = $(this);
        const submitButton = form.find('button[type="submit"]');
        const originalText = submitButton.html();
        
        console.log('Validando formulário de acordo...');
        
        // Validar campos básicos (por questões de segurança, faremos a validação no servidor também)
        if (!form[0].checkValidity()) {
            form[0].reportValidity();
            return false;
        }
        
        // Validar Assunção de Dívida
        if ($('#assuncao_divida').is(':checked') && !$('#assuntor_id').val()) {
                Swal.fire({
                    icon: 'error',
                title: 'Campo obrigatório',
                text: 'Por favor, selecione o associado assuntor para a Assunção de Dívida.'
                });
                return false;
        }
        
        // Obter valores para validação
        const tipoVencimento = $('#tipo_vencimento').val();
        const tipoCalculo = $('input[name="tipo_calculo"]:checked').val();
        
        // Criar FormData aqui
        const formData = new FormData(this);
        
        // Forçar tipo de cálculo manual se o tipo de vencimento for manual
        if (tipoVencimento === 'manual') {
            formData.set('tipo_calculo', 'manual');
        }

        // Para vencimento anual, extrair mês e dia da data de vencimento anual
        if (tipoVencimento === 'anual') {
            const dataVencimentoAnual = $('#data_vencimento_anual').val();
            console.log('Data de vencimento anual:', dataVencimentoAnual);
            
            if (dataVencimentoAnual) {
                // Extrair dia e mês da data no formato YYYY-MM-DD
                const dataParts = dataVencimentoAnual.split('-');
                if (dataParts.length === 3) {
                    const mes = parseInt(dataParts[1]);
                    const dia = parseInt(dataParts[2]);
                    
                    console.log('Extraído - Mês:', mes, 'Dia:', dia);
                    
                    // Adicionar ao formData
                    formData.set('mes_vencimento', mes);
                    formData.set('dia_vencimento', dia);
                }
            }
        }
        
        // Validar datas apenas para vencimento manual (não para cálculo manual)
            if (tipoVencimento === 'manual') {
            console.log('Validando datas para vencimento manual');
            const camposPendentes = [];
            $('#tabela_parcelas_manuais tbody tr').each(function() {
                const row = $(this);
                const dataVencimento = row.find('input.data-vencimento-manual').val();
                const numeroParcela = row.find('input[type="hidden"]').val();
                
                console.log('Validando data:', {
                    parcela: numeroParcela,
                    data: dataVencimento,
                    elemento: row.find('input.data-vencimento-manual').length > 0 ? 'encontrado' : 'não encontrado'
                });
                
                if (!dataVencimento) {
                    camposPendentes.push('Parcela ' + numeroParcela);
                }
            });
            
            if (camposPendentes.length > 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'Datas incompletas',
                    html: 'Por favor, preencha as datas de vencimento de todas as parcelas.<br><br>' +
                          'Campos pendentes: ' + camposPendentes.join(', ')
                });
                
                return false;
            }
        }
        
        // Validar valores para cálculo manual (tanto para vencimento manual quanto para cálculo manual)
        if (tipoVencimento === 'manual' || tipoCalculo === 'manual') {
            console.log('Validando valores para cálculo manual');
            
            // Para vencimento manual, precisamos do JSON das parcelas
            if (tipoVencimento === 'manual') {
                // Forçar atualização do JSON antes de validar
                const parcelas = atualizarJSONParcelasManuais();
                if (parcelas.length === 0) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Valores incorretos',
                        text: 'Por favor, preencha corretamente os valores e datas de todas as parcelas.'
                    });
                    return false;
                }
                
                // Adicionar o JSON das parcelas manuais ao formData
                formData.set('parcelas_manuais_json', JSON.stringify(parcelas));
            }
            // Para cálculo manual com vencimento automático, adicionar os valores manualmente
            else if (tipoCalculo === 'manual') {
                // Capturar os valores das parcelas
                console.log('Capturando valores das parcelas para cálculo manual');
                const quantidade = parseInt($('#quantidade_parcelas').val()) || 0;
                
                for (let i = 1; i <= quantidade; i++) {
                    const parcelaInput = $(`.parcela-manual[data-parcela="${i}"]`);
                    if (parcelaInput.length > 0) {
                        const valor = parcelaInput.inputmask('unmaskedvalue');
                        console.log(`Parcela ${i}: ${valor}`);
                        formData.append(`parcela-manual_${i}`, valor);
        } else {
                        console.warn(`Campo da parcela ${i} não encontrado`);
                    }
                }
            }
            
            if (!validarValoresParcelas()) {
                Swal.fire({
                    icon: 'error',
                    title: 'Valores incorretos',
                    text: 'O valor total das parcelas não corresponde ao valor restante do acordo. Por favor, ajuste os valores.'
                });
                
                return false;
            }
        }
        
        // Debug dos dados que serão enviados
        console.log('Dados do formulário:');
        for (let pair of formData.entries()) {
            console.log(pair[0] + ': ' + pair[1]);
        }

        // Desabilitar botão e mostrar loading
        submitButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Aguarde...');

        $.ajax({
            url: 'ajax/gerenciar_acordo.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('Resposta do servidor:', response); // Debug
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Sucesso!',
                        text: response.message
                    }).then(() => {
                        // Fechar todos os modais
                        $('.modal').modal('hide');
                        // Recarregar a página para atualizar a tabela
                        window.location.reload();
                    });
                } else {
                    console.error('Erro na resposta:', response); // Debug
                    Swal.fire('Erro!', response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Erro na requisição:', {xhr, status, error}); // Debug
                let errorMessage = 'Erro ao processar requisição.';
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    }
                } catch (e) {
                    console.error('Erro ao parsear resposta:', xhr.responseText);
                }
                Swal.fire('Erro!', errorMessage, 'error');
            },
            complete: function() {
                // Restaurar botão
                submitButton.prop('disabled', false).html(originalText);
            }
        });
    });

    // Inicializar validação jQuery Validate se estiver disponível
    if ($.fn.validate) {
        $('#formAcordo').validate({
            errorElement: 'div',
            errorClass: 'invalid-feedback',
            highlight: function(element) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function(element) {
                $(element).removeClass('is-invalid');
            },
            errorPlacement: function(error, element) {
                error.insertAfter(element);
            }
        });
    }

    // Adicionar botão de distribuição igual ao container de cálculo manual
    if (!$('#btn_distribuir_igualmente_calculo').length) {
        $('#container_calculo_manual .card-header').append(`
            <button type="button" id="btn_distribuir_igualmente_calculo" class="btn btn-sm btn-outline-primary float-right">
                <i class="fas fa-balance-scale mr-1"></i> Distribuir valor igualmente
            </button>
        `);
    }

    // Aplicar evento ao botão de distribuição igual para cálculo manual
    $('#btn_distribuir_igualmente_calculo').on('click', function() {
        const { valorRestante } = atualizarResumoValores();
        const quantidade = parseInt($('#quantidade_parcelas').val()) || 0;
        
        if (quantidade <= 0) {
            Swal.fire('Atenção', 'Informe a quantidade de parcelas primeiro.', 'warning');
            return;
        }
        
        const valorParcela = (valorRestante / quantidade).toFixed(2);
        
        console.log('Distribuindo valor (cálculo manual):', valorRestante, 'em', quantidade, 'parcelas de', valorParcela);
        
        // Para cálculo manual com vencimento automático
        $('.parcela-manual').each(function() {
            $(this).val(valorParcela).trigger('input');
        });
        
        // Forçar validação
        setTimeout(function() {
            validarValoresParcelas();
        }, 300);
    });

    // Monitorar alterações nos campos de vencimento
    $('#dia_vencimento, #data_vencimento_anual').on('change', function() {
        const tipoVencimento = $('#tipo_vencimento').val();
        const tipoCalculo = $('input[name="tipo_calculo"]:checked').val();
        
        console.log('Campo de vencimento alterado:', $(this).attr('id'));
        
        if (tipoCalculo === 'manual' && $('#quantidade_parcelas').val() > 0) {
            setTimeout(function() {
                gerarParcelasCalculoManual();
                $('#feedback_diferenca, #feedback_calculo_manual').show();
                validarValoresParcelas();
            }, 100);
        }
    });
    
    // Adicionar máscara para a data de vencimento anual se necessário
    if ($.fn.datepicker && $('#data_vencimento_anual').length) {
        $('#data_vencimento_anual').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            startDate: '+1y'
        });
    }

    // Controle do campo de Alvará a Receber
    $('#usar_alvara_receber').change(function() {
        if ($(this).is(':checked')) {
            $('#opcoes_alvara_receber').slideDown();
        } else {
            $('#opcoes_alvara_receber').slideUp();
            $('input[name="valor_alvara_receber"]').val('');
        }
        calcularValorParcelas();
    });

    // Atualizar cálculo quando o valor do alvará a receber mudar
    $('input[name="valor_alvara_receber"]').on('input', function() {
        calcularValorParcelas();
    });

    // Modificar a função calcularValorParcelas para considerar o alvará a receber
    function calcularValorParcelas() {
        let valorAcordo = parseFloat($('input[name="valor_acordo"]').val().replace(/[R$\s.]/g, '').replace(',', '.')) || 0;
        let valorEntrada = 0;
        let valorAlvaraReceber = 0;

        // Calcular valor da entrada
        if ($('#usar_entrada').is(':checked')) {
            if ($('#tipo_entrada').val() === 'valor' || $('#tipo_entrada').val() === 'misto') {
                valorEntrada += parseFloat($('input[name="valor_entrada"]').val().replace(/[R$\s.]/g, '').replace(',', '.')) || 0;
            }
            if ($('#tipo_entrada').val() === 'alvara' || $('#tipo_entrada').val() === 'misto') {
                let alvaraSelect = $('select[name="alvara_id"]');
                if (alvaraSelect.val()) {
                    valorEntrada += parseFloat(alvaraSelect.find(':selected').data('valor')) || 0;
                }
            }
        }

        // Calcular valor do alvará a receber
        if ($('#usar_alvara_receber').is(':checked')) {
            valorAlvaraReceber = parseFloat($('input[name="valor_alvara_receber"]').val().replace(/[R$\s.]/g, '').replace(',', '.')) || 0;
        }

        // Calcular valor restante considerando entrada e alvará a receber
        let valorRestante = valorAcordo - valorEntrada - valorAlvaraReceber;
        let quantidadeParcelas = parseInt($('#quantidade_parcelas').val()) || 1;
        let valorParcela = valorRestante / quantidadeParcelas;

        if (valorRestante <= 0) {
            $('#valor_parcela_container').hide();
            $('#valor_parcela_info').text('Não há valor restante para parcelamento');
        } else {
            $('#valor_parcela_container').show();
            $('#valor_parcela_info').html(`
                <div class="alert alert-info mb-0">
                    <strong>Resumo do cálculo:</strong><br>
                    Valor do acordo: R$ ${valorAcordo.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}<br>
                    Valor da entrada: R$ ${valorEntrada.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}<br>
                    Valor do alvará a receber: R$ ${valorAlvaraReceber.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}<br>
                    Valor restante: R$ ${valorRestante.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}<br>
                    Valor por parcela: R$ ${valorParcela.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </div>
            `);
        }
    }

    // Adicionar evento para recalcular quando a quantidade de parcelas mudar
    $('#quantidade_parcelas').on('change input', function() {
        calcularValorParcelas();
    });

    // Função para calcular valores do acordo
    function calcularValoresAcordo() {
        let valorAcordo = parseFloat($('input[name="valor_acordo"]').val().replace(/[R$\s.]/g, '').replace(',', '.')) || 0;
        let valorEntrada = 0;
        let valorAlvaraReceber = 0;

        // Calcular valor da entrada
        if ($('#usar_entrada').is(':checked')) {
            if ($('#tipo_entrada').val() === 'valor' || $('#tipo_entrada').val() === 'misto') {
                valorEntrada += parseFloat($('input[name="valor_entrada"]').val().replace(/[R$\s.]/g, '').replace(',', '.')) || 0;
            }
            if ($('#tipo_entrada').val() === 'alvara' || $('#tipo_entrada').val() === 'misto') {
                let alvaraSelect = $('select[name="alvara_id"]');
                if (alvaraSelect.val()) {
                    valorEntrada += parseFloat(alvaraSelect.find(':selected').data('valor')) || 0;
                }
            }
        }

        // Calcular valor do alvará a receber
        if ($('#usar_alvara_receber').is(':checked')) {
            valorAlvaraReceber = parseFloat($('input[name="valor_alvara_receber"]').val().replace(/[R$\s.]/g, '').replace(',', '.')) || 0;
        }

        // Calcular valor restante considerando entrada e alvará a receber
        let valorRestante = valorAcordo - valorEntrada - valorAlvaraReceber;
        let quantidadeParcelas = parseInt($('#quantidade_parcelas').val()) || 1;
        let valorParcela = valorRestante / quantidadeParcelas;

        return {
            valorAcordo,
            valorEntrada,
            valorAlvaraReceber,
            valorRestante,
            quantidadeParcelas,
            valorParcela
        };
    }

    // Função para atualizar o resumo dos valores
    function atualizarResumoValores() {
        const valores = calcularValoresAcordo();
        
        if (valores.valorRestante <= 0) {
            $('#valor_parcela_container').hide();
            $('#valor_parcela_info').text('Não há valor restante para parcelamento');
        } else {
            $('#valor_parcela_container').show();
            $('#valor_parcela_info').html(`
                <div class="alert alert-info mb-0">
                    <strong>Resumo do cálculo:</strong><br>
                    Valor do acordo: R$ ${valores.valorAcordo.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}<br>
                    Valor da entrada: R$ ${valores.valorEntrada.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}<br>
                    Valor do alvará a receber: R$ ${valores.valorAlvaraReceber.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}<br>
                    Valor restante: R$ ${valores.valorRestante.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}<br>
                    Valor por parcela: R$ ${valores.valorParcela.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </div>
            `);
        }

        return valores;
    }

    // Função para calcular valor das parcelas (mantida para compatibilidade)
    function calcularValorParcelas() {
        return atualizarResumoValores();
    }

    // Eventos para recalcular valores
    $('#quantidade_parcelas').on('change input', function() {
        const valores = atualizarResumoValores();
        
        // Se estiver no modo de cálculo manual, atualizar os campos
        if ($('input[name="tipo_calculo"]:checked').val() === 'manual') {
            $('.parcela-manual').each(function() {
                $(this).val(valores.valorParcela.toLocaleString('pt-BR', { minimumFractionDigits: 2 }));
            });
        }
    });

    // Atualizar valores quando mudar o tipo de cálculo
    $('input[name="tipo_calculo"]').on('change', function() {
        const valores = atualizarResumoValores();
        
        if (this.value === 'manual') {
            // Preencher campos manuais com o valor calculado
            $('.parcela-manual').each(function() {
                $(this).val(valores.valorParcela.toLocaleString('pt-BR', { minimumFractionDigits: 2 }));
            });
        }
    });

    // Eventos que devem disparar recálculo
    $('#usar_entrada, #tipo_entrada, select[name="alvara_id"], #usar_alvara_receber, input[name="valor_alvara_receber"], input[name="valor_entrada"], input[name="valor_acordo"]').on('change input', function() {
        atualizarResumoValores();
    });

    // Distribuir valores igualmente no modo manual
    $('#btn_distribuir_igualmente_calculo').on('click', function() {
        const valores = calcularValoresAcordo();
        
        if (valores.quantidadeParcelas <= 0) {
            Swal.fire('Atenção', 'Informe a quantidade de parcelas primeiro.', 'warning');
            return;
        }
        
        console.log('Distribuindo valor (cálculo manual):', valores.valorRestante, 'em', valores.quantidadeParcelas, 'parcelas de', valores.valorParcela);
        
        // Para cálculo manual com vencimento automático
        $('.parcela-manual').each(function() {
            $(this).val(valores.valorParcela.toLocaleString('pt-BR', { minimumFractionDigits: 2 })).trigger('input');
        });
        
        // Forçar validação
        setTimeout(function() {
            validarValoresParcelas();
        }, 300);
    });

    // Monitorar alterações no tipo de cálculo
    $('input[name="tipo_calculo"]').on('change', function() {
        atualizarOpcoesParcelas();
    });

    // Validar se o dia da data inicial corresponde ao dia de vencimento mensal
    $('#data_vencimento_inicial').on('change', function() {
        const dataInicial = $(this).val();
        if (!dataInicial) return;
        
        const diaVencimento = parseInt($('#dia_vencimento').val());
        if (!diaVencimento) return;
        
        const [ano, mes, dia] = dataInicial.split('-').map(Number);
        
        if (dia !== diaVencimento) {
            Swal.fire({
                title: 'Atenção',
                text: `O dia da data inicial (${dia}) deve ser igual ao dia de vencimento mensal (${diaVencimento}).`,
                icon: 'warning',
                confirmButtonText: 'Entendi'
            });
        }
        
        atualizarOpcoesParcelas();
    });
    
    // Atualizar data de vencimento inicial quando o dia de vencimento for alterado
    $('#dia_vencimento').on('change', function() {
        const diaVencimento = parseInt($(this).val());
        const dataInicial = $('#data_vencimento_inicial').val();
        
        if (dataInicial) {
            const [ano, mes, dia] = dataInicial.split('-').map(Number);
            if (dia !== diaVencimento) {
                const dataObj = new Date(ano, mes - 1, 1);
                const ultimoDia = new Date(ano, mes, 0).getDate();
                const novoVencimento = Math.min(diaVencimento, ultimoDia);
                
                dataObj.setDate(novoVencimento);
                
                const novoAno = dataObj.getFullYear();
                const novoMes = String(dataObj.getMonth() + 1).padStart(2, '0');
                const novoDia = String(dataObj.getDate()).padStart(2, '0');
                
                $('#data_vencimento_inicial').val(`${novoAno}-${novoMes}-${novoDia}`);
            }
        }
        
        // Atualizar o cálculo dos valores ao mudar o dia de vencimento
        atualizarResumoValores();
        
        atualizarOpcoesParcelas();
    });
});
</script> 
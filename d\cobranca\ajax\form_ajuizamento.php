<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

$processo_id = isset($_GET['processo_id']) ? intval($_GET['processo_id']) : 0;

// Verificar se tem acordos
$stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_acordos WHERE processo_id = ?");
$stmt->execute([$processo_id]);
$tem_acordos = $stmt->fetchColumn() > 0;

// Verificar se tem alvarás
$stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_alvaras WHERE processo_id = ?");
$stmt->execute([$processo_id]);
$tem_alvaras = $stmt->fetchColumn() > 0;

// Se tiver acordos ou alvarás, não permitir a edição
if ($tem_acordos || $tem_alvaras) {
    echo '<div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Não é possível editar as informações de ajuizamento deste processo pois ele possui acordos ou alvarás cadastrados.
          </div>';
    exit;
}

// Buscar informações do processo
$stmt = $pdo->prepare("
    SELECT p.*, m.nome as modalidade_nome
    FROM cbp_processos_judiciais p
    LEFT JOIN cbp_modalidades_processo m ON p.modalidade_id = m.id
    WHERE p.id = ?
");
$stmt->execute([$processo_id]);
$processo = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$processo) {
    echo '<div class="alert alert-danger">Processo não encontrado.</div>';
    exit;
}

// Formatar CPF/CNPJ
$cpf_cnpj = $processo['cpf_cnpj'];
if (strlen($cpf_cnpj) === 11) {
    $cpf_cnpj_formatado = substr($cpf_cnpj, 0, 3) . '.' . 
                         substr($cpf_cnpj, 3, 3) . '.' . 
                         substr($cpf_cnpj, 6, 3) . '-' . 
                         substr($cpf_cnpj, 9, 2);
} else {
    $cpf_cnpj_formatado = substr($cpf_cnpj, 0, 2) . '.' . 
                         substr($cpf_cnpj, 2, 3) . '.' . 
                         substr($cpf_cnpj, 5, 3) . '/' . 
                         substr($cpf_cnpj, 8, 4) . '-' . 
                         substr($cpf_cnpj, 12, 2);
}
?>

<form id="formAjuizamento" class="needs-validation" novalidate>
    <input type="hidden" name="processo_id" value="<?php echo $processo_id; ?>">
    
    <!-- Informações do Processo -->
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="mb-0">Informações do Processo</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Nome:</strong> <?php echo $processo['nome']; ?></p>
                    <p><strong>CPF/CNPJ:</strong> <?php echo $cpf_cnpj_formatado; ?></p>
                    <p><strong>Número do Processo:</strong> <?php echo $processo['numero_processo']; ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Modalidade:</strong> <?php echo $processo['modalidade_nome']; ?></p>
                    <p><strong>Número do Contrato:</strong> <?php echo $processo['numero_contrato']; ?></p>
                    <p><strong>Data de Envio:</strong> <?php echo date('d/m/Y', strtotime($processo['data_envio'])); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Informações do Ajuizamento -->
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="mb-0">Informações do Ajuizamento</h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">Data do Ajuizamento</label>
                    <input type="date" name="data_ajuizamento" class="form-control" required
                           value="<?php echo $processo['data_ajuizamento'] ?? ''; ?>">
                </div>
                <div class="col-md-6">
                    <label class="form-label">Valor Ajuizado</label>
                    <div class="input-group">
                        <span class="input-group-text">R$</span>
                        <input type="text" id="valor_ajuizado" name="valor_ajuizado" class="form-control" required
                               value="<?php echo $processo['valor_ajuizado'] ? number_format($processo['valor_ajuizado'], 2, ',', '.') : ''; ?>">
                    </div>
                    <small class="text-muted">Digite um valor maior que zero (ex: 18000 ou 18.000,00)</small>
                </div>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-end gap-2">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
        <button type="submit" class="btn btn-primary">Salvar Ajuizamento</button>
    </div>
</form>

<script>
$(document).ready(function() {
    // Não limpar o campo inicialmente se já tiver um valor
    var valorInicial = $('#valor_ajuizado').val();
    if (!valorInicial || valorInicial === '0' || valorInicial === '0,00') {
        $('#valor_ajuizado').val('');
    }
    
    // Aplicar máscara de formatação de moeda
    $('#valor_ajuizado').on('input', function(e) {
        // Obter o valor sem formatação (remover tudo que não for dígito)
        var value = $(this).val().replace(/\D/g, '');
        
        // Se não tiver valor, deixar vazio
        if (value === '') {
            $(this).val('');
            return;
        }
        
        // Converter para número (dividir por 100 para considerar centavos)
        value = parseFloat(value) / 100;
        
        // Formatar o valor para moeda brasileira
        var formattedValue = value.toLocaleString('pt-BR', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        
        // Atualizar o valor do campo
        $(this).val(formattedValue);
    });
    
    // Submissão do formulário
    $('#formAjuizamento').on('submit', function(e) {
        e.preventDefault();
        
        console.log('Formulário submetido');
        
        if (this.checkValidity()) {
            console.log('Formulário válido');
            
            try {
                // Elementos do DOM
                console.log('Elemento valor_ajuizado existe:', $('#valor_ajuizado').length > 0);
                
                // Obter o valor diretamente do DOM
                var valorInputField = document.getElementById('valor_ajuizado');
                var valorInputDOM = valorInputField ? valorInputField.value : 'campo não encontrado';
                console.log('Valor do DOM direto:', valorInputDOM);
                
                // Obter o valor usando jQuery
                var valorInput = $('#valor_ajuizado').val();
                console.log('Valor original (jQuery):', valorInput);
                
                // Verificar se está vazio
                if (!valorInput || valorInput.trim() === '' || valorInput === '0') {
                    console.log('Valor vazio ou zero detectado');
                    Swal.fire({
                        icon: 'error',
                        title: 'Campo obrigatório',
                        text: 'Por favor, informe o valor ajuizado.'
                    });
                    return;
                }
                
                // Tratamento extra para valor
                var valorParaLimpar = valorInput;
                if (valorParaLimpar.startsWith('R$')) {
                    valorParaLimpar = valorParaLimpar.substring(2).trim();
                }
                console.log('Valor após remover prefixo R$:', valorParaLimpar);
                
                // Limpar formatação (remover pontos e substituir vírgula por ponto)
                var valorLimpo = valorParaLimpar.replace(/\./g, '').replace(',', '.');
                console.log('Valor após limpeza:', valorLimpo);
                
                // Converter para número
                var valorAjuizado = parseFloat(valorLimpo);
                console.log('Valor convertido:', valorAjuizado);
                
                // Validar valor
                if (isNaN(valorAjuizado) || valorAjuizado <= 0) {
                    console.log('Valor inválido detectado:', valorAjuizado);
                    Swal.fire({
                        icon: 'error',
                        title: 'Valor inválido',
                        text: 'O valor ajuizado deve ser maior que zero.'
                    });
                    return;
                }

                // Obter os valores do formulário
                var formData = {
                    processo_id: $('input[name="processo_id"]').val(),
                    data_ajuizamento: $('input[name="data_ajuizamento"]').val(),
                    valor_ajuizado: valorAjuizado
                };

                // Validar data de ajuizamento
                if (!formData.data_ajuizamento) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro!',
                        text: 'Por favor, informe a data de ajuizamento.'
                    });
                    return;
                }

                // Debug dados completos
                console.log('Dados do formulário:', formData);

                // Enviar dados para o servidor
                $.post('ajax/processar_ajuizamento.php', formData)
                .done(function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Sucesso!',
                            text: response.message
                        }).then(() => {
                            $('#modalAjuizamento').modal('hide');
                            // Recarregar o formulário de edição para atualizar os dados
                            editarProcesso(<?php echo $processo_id; ?>);
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Erro!',
                            text: response.message
                        });
                    }
                })
                .fail(function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro!',
                        text: 'Ocorreu um erro ao processar a requisição.'
                    });
                });
            } catch (error) {
                console.error('Erro ao processar o valor:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Erro!',
                    text: 'Ocorreu um erro ao processar o valor informado.'
                });
            }
        }
        
        $(this).addClass('was-validated');
    });
});
</script> 
<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$associado_id = isset($_GET['associado_id']) ? intval($_GET['associado_id']) : 0;

// Se for edição, buscar dados do contrato
if ($id > 0) {
    $stmt = $pdo->prepare("
        SELECT c.*, m.nome as modalidade_nome, a.nome as associado_nome, a.cpf_cnpj as associado_documento
        FROM cbp_contratos c
        LEFT JOIN cbp_modalidades_processo m ON c.modalidade_id = m.id
        LEFT JOIN cbp_associados a ON c.associado_id = a.id
        WHERE c.id = ?
    ");
    $stmt->execute([$id]);
    $contrato = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$contrato) {
        echo '<div class="alert alert-danger">Contrato não encontrado.</div>';
        exit;
    }
    
    $associado_id = $contrato['associado_id'];
}

// Se tiver associado_id, buscar dados do associado
$associado = null;
if ($associado_id > 0) {
    $stmt = $pdo->prepare("
        SELECT a.*, pa.nome as pa_nome, pa.numero as pa_numero
        FROM cbp_associados a
        LEFT JOIN pontos_atendimento pa ON a.pa_id = pa.id
        WHERE a.id = ?
    ");
    $stmt->execute([$associado_id]);
    $associado = $stmt->fetch(PDO::FETCH_ASSOC);
}

// Buscar modalidades
$stmt = $pdo->query("SELECT id, nome FROM cbp_modalidades_processo ORDER BY nome");
$modalidades = $stmt->fetchAll();

// Buscar PAs
$stmt = $pdo->query("SELECT id, nome, numero FROM pontos_atendimento ORDER BY nome");
$pontos_atendimento = $stmt->fetchAll();

// Buscar advogados
$stmt = $pdo->query("SELECT id, nome FROM cbp_advogados ORDER BY nome");
$advogados = $stmt->fetchAll();

// Função para formatar documento
function formatarDocumento($doc) {
    $doc = preg_replace('/[^0-9]/', '', $doc);
    if (strlen($doc) === 11) {
        return preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $doc);
    } else if (strlen($doc) === 14) {
        return preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $doc);
    }
    return $doc;
}
?>

<form id="formContrato" class="needs-validation" novalidate>
    <?php if ($id): ?>
        <input type="hidden" name="id" value="<?= $id ?>">
    <?php endif; ?>

    <div class="row g-3 mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Dados do Associado</h5>
                </div>
                <div class="card-body">
                    <?php if ($associado): ?>
                        <input type="hidden" name="associado_id" value="<?= $associado['id'] ?>">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label class="form-label">Nome do Associado</label>
                                <div class="d-flex align-items-center">
                                    <div class="form-control bg-light"><?= htmlspecialchars($associado['nome']) ?></div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">CPF/CNPJ</label>
                                <div class="form-control bg-light"><?= formatarDocumento($associado['cpf_cnpj']) ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">PA (Ponto de Atendimento)</label>
                                <div class="form-control bg-light"><?= htmlspecialchars($associado['pa_nome']) ?> (<?= $associado['pa_numero'] ?>)</div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <p class="mb-3">Selecione um associado para continuar o cadastro do contrato.</p>
                            <button type="button" class="btn btn-primary" onclick="selecionarAssociado()">
                                <i class="fas fa-user-plus me-2"></i> Selecionar Associado
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php if ($associado): ?>
    <div class="row g-3">
        <div class="col-md-6">
            <label class="form-label">Número do Contrato</label>
            <input type="text" name="numero_contrato" class="form-control" value="<?= $contrato['numero_contrato'] ?? '' ?>" required>
            <div class="invalid-feedback">
                Por favor, informe o número do contrato.
            </div>
        </div>

        <div class="col-md-6">
            <label class="form-label">Modalidade</label>
            <select name="modalidade_id" class="form-select" required>
                <option value="">Selecione...</option>
                <?php foreach ($modalidades as $modalidade): ?>
                    <option value="<?= $modalidade['id'] ?>" <?= (isset($contrato['modalidade_id']) && $contrato['modalidade_id'] == $modalidade['id']) ? 'selected' : '' ?>>
                        <?= $modalidade['nome'] ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <div class="invalid-feedback">
                Por favor, selecione a modalidade.
            </div>
        </div>

        <div class="col-md-12">
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="possui_garantia_real" name="possui_garantia_real" value="1" <?= (isset($contrato['possui_garantia_real']) && $contrato['possui_garantia_real']) ? 'checked' : '' ?>>
                <label class="form-check-label" for="possui_garantia_real">Possui garantia real?</label>
            </div>
        </div>

        <div class="col-md-12" id="div_tipo_garantia" style="<?= (isset($contrato['possui_garantia_real']) && $contrato['possui_garantia_real']) ? '' : 'display: none;' ?>">
            <label class="form-label">Tipo de Garantia</label>
            <div class="form-check">
                <input type="radio" class="form-check-input" id="tipo_garantia_movel" name="tipo_garantia" value="movel" <?= (isset($contrato['tipo_garantia']) && $contrato['tipo_garantia'] == 'movel') ? 'checked' : '' ?>>
                <label class="form-check-label" for="tipo_garantia_movel">Bem Móvel</label>
            </div>
            <div class="form-check">
                <input type="radio" class="form-check-input" id="tipo_garantia_imovel" name="tipo_garantia" value="imovel" <?= (isset($contrato['tipo_garantia']) && $contrato['tipo_garantia'] == 'imovel') ? 'checked' : '' ?>>
                <label class="form-check-label" for="tipo_garantia_imovel">Bem Imóvel</label>
            </div>
        </div>

        <div class="col-md-12" id="div_descricao_bem" style="<?= (isset($contrato['tipo_garantia']) && !empty($contrato['tipo_garantia'])) ? '' : 'display: none;' ?>">
            <label class="form-label">Descrição do Bem</label>
            <textarea name="descricao_bem" class="form-control" rows="3" placeholder="Descreva detalhes sobre o bem..."><?= $contrato['descricao_bem'] ?? '' ?></textarea>
            <div class="invalid-feedback">
                Por favor, descreva o bem.
            </div>
        </div>

        <div class="col-md-6">
            <label class="form-label">Data de Envio</label>
            <input type="date" name="data_envio" class="form-control" value="<?= $contrato['data_envio'] ?? '' ?>" required>
            <div class="invalid-feedback">
                Por favor, informe a data de envio.
            </div>
        </div>

        <div class="col-md-6">
            <label class="form-label">Advogado</label>
            <select name="advogado_id" class="form-select" required>
                <option value="">Selecione o advogado...</option>
                <?php foreach ($advogados as $advogado): ?>
                    <option value="<?= $advogado['id'] ?>" <?= (isset($contrato['advogado_id']) && $contrato['advogado_id'] == $advogado['id']) ? 'selected' : '' ?>>
                        <?= htmlspecialchars($advogado['nome']) ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <div class="invalid-feedback">
                Por favor, selecione o advogado.
            </div>
        </div>
    </div>
    <?php endif; ?>
</form>

<script>
    // Mostrar/esconder campo de tipo de garantia
    $('#possui_garantia_real').on('change', function() {
        if ($(this).is(':checked')) {
            $('#div_tipo_garantia').slideDown();
        } else {
            $('#div_tipo_garantia').slideUp();
            $('#div_descricao_bem').slideUp();
            $('input[name="tipo_garantia"]').prop('checked', false);
            $('textarea[name="descricao_bem"]').val('');
        }
    });

    // Mostrar/esconder campo de descrição do bem
    $('input[name="tipo_garantia"]').on('change', function() {
        if ($(this).is(':checked')) {
            $('#div_descricao_bem').slideDown();
        } else {
            $('#div_descricao_bem').slideUp();
            $('textarea[name="descricao_bem"]').val('');
        }
    });

    // Função para abrir o modal de seleção de associado
    function selecionarAssociado() {
        // Aqui você chamaria uma função para abrir o modal de seleção
        $('#modalSelecionarAssociado').modal('show');
    }
</script> 
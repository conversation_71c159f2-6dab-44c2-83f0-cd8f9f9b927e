<?php
// Habilitar exibição de erros para debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../../auth_check.php';
require_once '../../config/database.php';
require_once '../verificar_acesso.php';

// Log para debug
error_log('Iniciando form_processo.php');
error_log('ID recebido: ' . (isset($_GET['id']) ? $_GET['id'] : 'não definido'));

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Se for edição, buscar dados do processo
if ($id > 0) {
    try {
        $stmt = $pdo->prepare("
            SELECT p.*,
                   pa.nome as pa_nome,
                   m.nome as modalidade_nome,
                   a.nome as advogado_nome,
                   s.nome as status_nome,
                   DATE_FORMAT(p.data_ajuizamento, '%Y-%m-%d') as data_ajuizamento_formatada
            FROM cbp_processos_judiciais p
            LEFT JOIN cbp_advogados a ON p.advogado_id = a.id
            LEFT JOIN pontos_atendimento pa ON p.pa_id = pa.id
            LEFT JOIN cbp_modalidades_processo m ON p.modalidade_id = m.id
            LEFT JOIN cbp_status_processo s ON p.status_id = s.id
            WHERE p.id = ?
        ");
        $stmt->execute([$id]);
        $processo = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$processo) {
            throw new Exception('Processo não encontrado');
        }

        // Buscar contratos vinculados ao processo
        $stmt = $pdo->prepare("
            SELECT 
                c.id, 
                c.numero_contrato, 
                c.associado_id,
                a.nome as associado_nome,
                a.cpf_cnpj as associado_documento,
                pc.valor_contrato, 
                m.nome as modalidade_nome
            FROM cbp_contratos c
            INNER JOIN cbp_processos_contratos pc ON c.id = pc.contrato_id
            INNER JOIN cbp_associados a ON c.associado_id = a.id
            LEFT JOIN cbp_modalidades_processo m ON c.modalidade_id = m.id
            WHERE pc.processo_id = ?
        ");
        $stmt->execute([$id]);
        $contratos = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Buscar alvarás do processo
        $stmt = $pdo->prepare("
            SELECT *,
                   DATE_FORMAT(data_recebimento, '%Y-%m-%d') as data_alvara_formatada
            FROM cbp_alvaras 
            WHERE processo_id = ?
            ORDER BY data_recebimento DESC
        ");
        $stmt->execute([$id]);
        $alvaras = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calcular valor total dos contratos vinculados
        $stmt = $pdo->prepare("
            SELECT COALESCE(SUM(pc.valor_contrato), 0) as valor_total
            FROM cbp_processos_contratos pc
            WHERE pc.processo_id = ?
        ");
        $stmt->execute([$id]);
        $valor_total_contratos = $stmt->fetchColumn();

    } catch (Exception $e) {
        error_log('Erro ao buscar processo: ' . $e->getMessage());
        echo '<div class="alert alert-danger">Erro ao carregar dados do processo: ' . $e->getMessage() . '</div>';
        exit;
    }
}

try {
    // Buscar advogados
    $stmt = $pdo->query("SELECT id, nome, oab FROM cbp_advogados WHERE ativo = 1 ORDER BY nome");
    $advogados = $stmt->fetchAll();

    // Buscar status
    $stmt = $pdo->query("SELECT id, nome FROM cbp_status_processo ORDER BY nome");
    $status_list = $stmt->fetchAll();

    // Buscar modalidades
    $stmt = $pdo->query("SELECT id, nome FROM cbp_modalidades_processo ORDER BY nome");
    $modalidades = $stmt->fetchAll();

    // Buscar pontos de atendimento
    $stmt = $pdo->query("SELECT id, nome, numero FROM pontos_atendimento ORDER BY nome");
    $pontos_atendimento = $stmt->fetchAll();

} catch (Exception $e) {
    error_log('Erro ao buscar dados auxiliares: ' . $e->getMessage());
    echo '<div class="alert alert-danger">Erro ao carregar dados auxiliares: ' . $e->getMessage() . '</div>';
    exit;
}

// Função para formatar documento
function formatarDocumento($doc) {
    $doc = preg_replace('/[^0-9]/', '', $doc);
    if (strlen($doc) === 11) {
        return substr($doc, 0, 3) . '.' . 
               substr($doc, 3, 3) . '.' . 
               substr($doc, 6, 3) . '-' . 
               substr($doc, 9, 2);
    } else if (strlen($doc) === 14) {
        return substr($doc, 0, 2) . '.' . 
               substr($doc, 2, 3) . '.' . 
               substr($doc, 5, 3) . '/' . 
               substr($doc, 8, 4) . '-' . 
               substr($doc, 12, 2);
    }
    return $doc;
}

// Função para formatar valor monetário
function formatarValor($valor) {
    return 'R$ ' . number_format($valor, 2, ',', '.');
}
?>

<form id="formProcesso" class="needs-validation" novalidate>
        <input type="hidden" name="id" value="<?= $id ?>">
    <input type="hidden" name="nome" value="<?= $processo['nome'] ?? '' ?>">
    <input type="hidden" name="cpf_cnpj" value="<?= $processo['cpf_cnpj'] ?? '' ?>">
    <input type="hidden" name="pa_id" value="<?= $processo['pa_id'] ?? '' ?>">
    <input type="hidden" name="numero_contrato" value="<?= $processo['numero_contrato'] ?? '' ?>">
    <input type="hidden" name="modalidade_id" value="<?= $processo['modalidade_id'] ?? '' ?>">
    <input type="hidden" name="data_distribuicao" value="<?= $processo['data_distribuicao'] ?? date('Y-m-d') ?>">
    <input type="hidden" name="data_envio" value="<?= $processo['data_envio'] ?? date('Y-m-d') ?>">

    <!-- Seção de Informações Gerais -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Informações Gerais</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Advogado</label>
                                <select name="advogado_id" class="form-select select2" required>
                                    <option value="">Selecione...</option>
                                    <?php foreach ($advogados as $advogado): ?>
                                        <option value="<?= $advogado['id'] ?>" <?= ($advogado['id'] == $processo['advogado_id']) ? 'selected' : '' ?>>
                                            <?= $advogado['nome'] ?><?= !empty($advogado['oab']) ? ' (' . $advogado['oab'] . ')' : '' ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Por favor, selecione o advogado.
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Observações</label>
                                <textarea name="observacoes" class="form-control" rows="3"><?= $processo['observacoes'] ?? '' ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- Seção de Status do Processo -->
            <div class="card mb-4">
                <div class="card-header">
            <h5 class="mb-0">Status do Processo</h5>
                </div>
                <div class="card-body">
            <div class="card border-left-warning shadow py-2">
                <div class="card-body">
                    <div class="form-check form-switch">
                        <input type="hidden" name="status_id" value="<?php echo $processo['status_id']; ?>">
                        <input class="form-check-input" type="checkbox" id="retomado" name="retomado" value="1" 
                            <?php echo (isset($processo['status_id']) && $processo['status_id'] == 4) ? 'checked' : ''; ?> 
                            <?php echo (isset($processo['status_id']) && ($processo['status_id'] == 2 || $processo['status_id'] == 4)) ? '' : 'disabled'; ?>>
                        <label class="form-check-label fw-bold text-warning" for="retomado">RETOMADO</label>
                        <?php if (!(isset($processo['status_id']) && ($processo['status_id'] == 2 || $processo['status_id'] == 4))): ?>
                        <small class="text-muted d-block mt-2">
                            O status RETOMADO só pode ser alterado quando o processo está em ACORDO JUDICIAL.
                        </small>
                        <?php endif; ?>
                        <?php
                        // Verificar se existe acordo ativo
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_acordos WHERE processo_id = ? AND ativo = 1");
                        $stmt->execute([$id]);
                        $tem_acordo_ativo = $stmt->fetchColumn() > 0;
                        if ($tem_acordo_ativo): ?>
                        <div class="alert alert-info py-1 px-2 mt-2 mb-0">
                            <i class="fas fa-info-circle me-1"></i> Este processo possui um acordo ativo. Marcando esta opção o acordo atual será inativado.
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Seção de Ajuizamento -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Informações de Ajuizamento</h5>
            <?php if (!isset($processo['data_ajuizamento']) || empty($processo['data_ajuizamento'])): ?>
                <button type="button" class="btn btn-primary btn-sm" onclick="incluirAjuizamento()">
                    <i class="fas fa-plus"></i> Incluir Ajuizamento
                </button>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <input type="hidden" id="tem_ajuizamento" value="<?= (isset($processo['data_ajuizamento']) && !empty($processo['data_ajuizamento'])) ? '1' : '0' ?>">
            <?php if (isset($processo['data_ajuizamento']) && !empty($processo['data_ajuizamento'])): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                <th>Número do Processo</th>
                                <th>Data de Ajuizamento</th>
                                <th>Valor Ajuizado</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td><?= $processo['numero_processo'] ?? '' ?></td>
                                <td><?= $processo['data_ajuizamento_formatada'] ?? '' ?></td>
                                <td><?= isset($processo['valor_ajuizado']) ? formatarValor($processo['valor_ajuizado']) : '' ?></td>
                                <td>
                                    <?php if (TIPO_ACESSO_COBRANCA === 'GESTOR'): ?>
                                    <button type="button" class="btn btn-sm btn-danger" onclick="removerAjuizamento(<?= $id ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                    <?php endif; ?>
                                        </td>
                                    </tr>
                            </tbody>
                        </table>
                    </div>
            <?php else: ?>
                <div class="alert alert-info">
                    Nenhum ajuizamento cadastrado para este processo.
                </div>
            <?php endif; ?>
            </div>
        </div>

    <!-- Seção de Contratos -->
            <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Contratos Vinculados</h5>
            <?php if (isset($processo['data_ajuizamento']) && !empty($processo['data_ajuizamento'])): ?>
                <button type="button" class="btn btn-primary btn-sm" onclick="vincularContrato()">
                    <i class="fas fa-plus"></i> Vincular Contrato
                </button>
            <?php else: ?>
                <button type="button" class="btn btn-primary btn-sm" disabled title="É necessário cadastrar o ajuizamento antes de vincular contratos">
                    <i class="fas fa-plus"></i> Vincular Contrato
                </button>
            <?php endif; ?>
                </div>
                <div class="card-body">
            <?php if (!isset($processo['data_ajuizamento']) || empty($processo['data_ajuizamento'])): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> É necessário cadastrar o ajuizamento antes de vincular contratos.
                            </div>
            <?php endif; ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover table-sm">
                            <thead>
                                <tr>
                                    <th style="width: 25%">Nome</th>
                                    <th style="width: 15%">CPF/CNPJ</th>
                                    <th style="width: 15%">Contrato</th>
                                    <th style="width: 15%">Modalidade</th>
                                    <th style="width: 15%" class="text-end">Valor</th>
                                    <th style="width: 15%" class="text-center">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                        <?php if (isset($contratos) && count($contratos) > 0): ?>
                            <?php 
                            $valor_total_contratos = 0;
                            foreach ($contratos as $contrato): 
                                $valor_total_contratos += $contrato['valor_contrato'];
                            ?>
                                    <tr>
                                        <td class="text-truncate" style="max-width: 150px;" title="<?= htmlspecialchars($contrato['associado_nome']) ?>"><?= htmlspecialchars($contrato['associado_nome']) ?></td>
                                        <td><?= formatarDocumento($contrato['associado_documento']) ?></td>
                                        <td class="text-truncate" style="max-width: 100px;" title="<?= htmlspecialchars($contrato['numero_contrato']) ?>"><?= htmlspecialchars($contrato['numero_contrato']) ?></td>
                                        <td class="text-truncate" style="max-width: 100px;" title="<?= htmlspecialchars($contrato['modalidade_nome']) ?>"><?= htmlspecialchars($contrato['modalidade_nome']) ?></td>
                                        <td class="text-end"><?= formatarValor($contrato['valor_contrato']) ?></td>
                                        <td class="text-center">
                                            <?php if (TIPO_ACESSO_COBRANCA === 'GESTOR'): ?>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="removerContrato(<?= $contrato['id'] ?>)">
                                            <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php if (isset($processo['valor_ajuizado']) && $processo['valor_ajuizado'] > 0): ?>
                                <tr class="table-light">
                                    <td colspan="4" class="text-end"><strong>Total dos Contratos:</strong></td>
                                    <td class="text-end"><strong><?= formatarValor($valor_total_contratos) ?></strong></td>
                                    <td></td>
                                </tr>
                                <tr class="table-light">
                                    <td colspan="4" class="text-end"><strong>Valor do Ajuizamento:</strong></td>
                                    <td class="text-end"><strong><?= formatarValor($processo['valor_ajuizado']) ?></strong></td>
                                    <td></td>
                                </tr>
                                <?php if (abs($valor_total_contratos - $processo['valor_ajuizado']) >= 0.01): ?>
                                    <tr>
                                        <td colspan="6">
                                            <div class="alert alert-warning mb-0">
                                                <i class="fas fa-exclamation-triangle"></i> 
                                                Os botões "Quitar Processo" e "Incluir Acordo" serão habilitados somente quando o valor total dos contratos vinculados for igual ao valor do ajuizamento.
                        </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center">Nenhum contrato vinculado a este processo.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                        </table>
                            </div>
                            </div>
                        </div>

    <!-- Seção de Alvarás -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Alvarás</h5>
                    <button type="button" class="btn btn-primary btn-sm" onclick="novoAlvara()">
                        <i class="fas fa-plus"></i> Novo Alvará
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover table-alvaras">
                            <thead>
                                <tr>
                                    <th style="width: 20%;">Data do Alvará</th>
                                    <th style="width: 20%;">Valor</th>
                                    <th style="width: 45%;">Observações</th>
                                    <th style="width: 15%;">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                        <?php if (isset($alvaras) && count($alvaras) > 0): ?>
                                <?php foreach ($alvaras as $alvara): ?>
                                    <tr>
                                        <td><?= $alvara['data_alvara_formatada'] ?></td>
                                    <td>R$ <?= number_format($alvara['valor'], 2, ',', '.') ?></td>
                                    <td>
                                        <?php
                                        $observacoes = $alvara['observacoes'];
                                        if (strlen($observacoes) > 60) {
                                            $observacoes_truncadas = substr($observacoes, 0, 60) . '...';
                                            echo '<span title="' . htmlspecialchars($observacoes) . '" data-bs-toggle="tooltip" data-bs-placement="top">' . htmlspecialchars($observacoes_truncadas) . '</span>';
                                        } else {
                                            echo htmlspecialchars($observacoes);
                                        }
                                        ?>
                                    </td>
                                        <td>
                                            <?php if (TIPO_ACESSO_COBRANCA === 'GESTOR'): ?>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="excluirAlvara(<?= $alvara['id'] ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-center">Nenhum alvará registrado para este processo.</td>
                                    </tr>
                        <?php endif; ?>
                                </tbody>
                            </table>
            </div>
        </div>
    </div>

    <div class="text-end mt-3">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> Salvar
        </button>
    </div>
</form>

<!-- Estilos para tabela de alvarás -->
<style>
.table-alvaras td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 0;
}
.table-alvaras td:nth-child(3) {
    white-space: normal;
    word-wrap: break-word;
}
</style>

<!-- Scripts necessários -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Funções para abrir os modais
function vincularContrato() {
    // Verificar se existe ajuizamento usando o input hidden
    if ($('#tem_ajuizamento').val() !== '1') {
        Swal.fire({
            icon: 'warning',
            title: 'Atenção!',
            text: 'É necessário cadastrar o ajuizamento antes de vincular contratos.'
        });
        return;
    }

    // Inicializar Select2 antes de mostrar o modal
    $('#contrato_id').select2({
        dropdownParent: $('#modalContrato'),
        ajax: {
            url: 'buscar_contratos.php',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    search: params.term,
                    page: params.page || 1,
                    processo_id: $('#processo_id').val()
                };
            },
            processResults: function(data, params) {
                params.page = params.page || 1;
                return {
                    results: data.results,
                    pagination: {
                        more: data.pagination.more
                    }
                };
            },
            cache: true
        },
        placeholder: 'Selecione um contrato',
        minimumInputLength: 0,
        language: {
            inputTooShort: function() {
                return 'Digite para buscar...';
            },
            searching: function() {
                return 'Buscando...';
            },
            noResults: function() {
                return 'Nenhum resultado encontrado';
            }
        }
    });

    // Esconder o modal principal
    $('#modalProcesso').modal('hide');
    
    // Mostrar o modal de contrato
    $('#modalContrato').modal('show');
}

function incluirAjuizamento() {
    console.log('Função incluirAjuizamento chamada');
    $('#modalProcesso').modal('hide');
    $('#modalAjuizamento').modal('show');
}

function novoAlvara() {
    console.log('Função novoAlvara chamada');
    $('#modalProcesso').modal('hide');
    $('#modalAlvara').modal('show');
}

// Função para validar o valor do contrato
function validarValorContrato() {
    const valorAjuizado = parseFloat($('#valor_ajuizado').val());
    const valorTotalContratos = parseFloat($('#valor_total_contratos').val());
    const valorContrato = parseFloat($('#valor_contrato').val().replace(/[^\d,]/g, '').replace(',', '.'));
    const valorDisponivel = valorAjuizado - valorTotalContratos;
    const TOLERANCIA = 0.01; // Tolerância de 1 centavo

    // Remover feedback anterior
    $('#valor_contrato').removeClass('is-invalid is-valid');
    $('#valor_contrato_feedback').remove();

    if (isNaN(valorContrato) || valorContrato <= 0) {
        $('#valor_contrato').addClass('is-invalid');
        $('#valor_contrato').after(`<div id="valor_contrato_feedback" class="invalid-feedback">Informe um valor válido maior que zero.</div>`);
        return false;
    }

    // Usar tolerância na comparação
    if (valorContrato - valorDisponivel > TOLERANCIA) {
        $('#valor_contrato').addClass('is-invalid');
        $('#valor_contrato').after(`<div id="valor_contrato_feedback" class="invalid-feedback">O valor não pode ser maior que o valor disponível (${formatarValor(valorDisponivel)})</div>`);
        return false;
    }

    // Se passou nas validações, mostrar feedback positivo
    $('#valor_contrato').addClass('is-valid');
    $('#valor_contrato').after(`<div id="valor_contrato_feedback" class="valid-feedback">Valor válido</div>`);
    return true;
}

// Função para salvar contrato
function salvarContrato() {
    if (!validarValorContrato()) {
        return;
    }

    const form = $('#formContrato');
    const formData = form.serialize();
    
    $.ajax({
        url: '../cobranca/ajax/vincular_contrato.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Sucesso!',
                    text: response.message,
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        $('#modalContrato').modal('hide');
                        $('#modalProcesso').modal('hide');
                    location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro!',
                    text: response.message || 'Erro ao vincular contrato'
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('Erro na requisição:', error);
            console.error('Status:', status);
            console.error('Resposta:', xhr.responseText);
            
            try {
                const response = JSON.parse(xhr.responseText);
                Swal.fire({
                    icon: 'error',
                    title: 'Erro!',
                    text: response.message || 'Erro ao vincular contrato'
                });
            } catch (e) {
            Swal.fire({
                icon: 'error',
                title: 'Erro!',
                text: 'Erro ao vincular contrato. Por favor, tente novamente.'
            });
            }
        }
    });
}

// Atualizar valor disponível quando o valor do contrato for alterado
$('#valor_contrato').on('input', function() {
    const valorContrato = parseFloat($(this).val().replace(/[^\d,]/g, '').replace(',', '.'));
    if (!isNaN(valorContrato)) {
        validarValorContrato();
    }
});

// Funções para salvar os dados
function salvarAjuizamento() {
    const form = $('#formAjuizamento');
    const formData = form.serialize();
    
    $.ajax({
        url: 'ajax/salvar_ajuizamento.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            console.log('Resposta do servidor:', response);
            if (response.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Sucesso!',
                    text: response.message,
                    showConfirmButton: false,
                    timer: 1500
                }).then(() => {
                $('#modalAjuizamento').modal('hide');
                $('#modalProcesso').modal('show');
                location.reload();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Erro!',
                    text: response.message || 'Erro ao salvar ajuizamento'
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('Erro na requisição:', error);
            console.error('Status:', status);
            console.error('Resposta:', xhr.responseText);
            
            try {
                const response = JSON.parse(xhr.responseText);
                Swal.fire({
                    icon: 'error',
                    title: 'Erro!',
                    text: response.message || 'Não foi possível salvar o ajuizamento.'
                });
            } catch (e) {
                Swal.fire({
                    icon: 'error',
                    title: 'Erro!',
                    text: 'Não foi possível salvar o ajuizamento.'
                });
            }
        }
    });
}

function salvarAlvara() {
    const form = $('#formAlvara');
    const formData = form.serialize();
    
    $.ajax({
        url: '../cobranca/ajax/salvar_alvara.php',
        type: 'POST',
        data: formData,
        success: function(response) {
            if (response.success) {
                // Exibir mensagem de sucesso
                Swal.fire({
                    icon: 'success',
                    title: 'Sucesso!',
                    text: response.message,
                    showConfirmButton: false,
                    timer: 1500
                }).then(() => {
                    // Fechar modais e recarregar a página
                    $('#modalAlvara').modal('hide');
                    $('#modalProcesso').modal('hide');
                    location.reload();
                });
            } else {
                // Exibir mensagem de erro
                Swal.fire({
                    icon: 'error',
                    title: 'Erro!',
                    text: response.message || 'Erro ao salvar alvará'
                });
            }
        },
        error: function() {
            Swal.fire({
                icon: 'error',
                title: 'Erro!',
                text: 'Erro ao salvar alvará'
            });
        }
    });
}

// Inicialização dos componentes
$(document).ready(function() {
    // Inicializar tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Inicializa as máscaras após garantir que o plugin está carregado
    if ($.fn.mask) {
    $('.dinheiro').mask('#.##0,00', {reverse: true});
    $('.processo').mask('0000000-00.0000.0.00.0000');
    } else {
        console.error('jQuery Mask plugin não está carregado');
    }
    
    // Adiciona o evento para o toggle de status RETOMADO
    $('#retomado').on('change', function() {
        if ($(this).is(':checked')) {
            Swal.fire({
                title: 'Atenção!',
                text: 'Ao marcar o processo como RETOMADO, todos os acordos ativos serão inativados. Deseja continuar?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Sim, continuar',
                cancelButtonText: 'Não, cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Usuário confirmou, manter o checkbox marcado
                } else {
                    // Usuário cancelou, desmarcar o checkbox
                    $(this).prop('checked', false);
                }
            });
        }
    });

    // Configura o envio do formulário
    $('#formProcesso').on('submit', function(e) {
        e.preventDefault();
        
        // Criar FormData com os valores do formulário
        const formData = new FormData(this);
        
        // Adicionar o status do checkbox RETOMADO
        formData.append('retomado', $('#retomado').is(':checked') ? '1' : '0');
        
        // Enviar o formulário via AJAX
        $.ajax({
            url: 'ajax/salvar_processo.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                try {
                    // Garantir que a resposta seja um objeto
                    const data = typeof response === 'string' ? JSON.parse(response) : response;
                    
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Sucesso!',
                            text: data.message,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            $('#modalProcesso').modal('hide');
                            location.reload();
                        });
                    } else {
            Swal.fire({
                icon: 'error',
                title: 'Erro!',
                            text: data.message || 'Erro ao salvar o processo'
                        });
                    }
                } catch (error) {
                    console.error('Erro ao processar resposta:', error);
                    console.error('Resposta recebida:', response);
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro!',
                        text: 'Erro ao processar resposta do servidor'
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('Erro na requisição:', error);
                console.error('Status:', status);
                console.error('Resposta:', xhr.responseText);
                
                Swal.fire({
                    icon: 'error',
                    title: 'Erro!',
                    text: 'Erro ao salvar o processo: ' + (xhr.responseJSON?.message || error)
                });
            }
    });
});

    // Configura os modais
    $('.modal').on('hidden.bs.modal', function() {
        console.log('Modal fechado:', this.id);
        if (this.id !== 'modalProcesso') {
            $('#modalProcesso').modal('show');
        }
    });

    // Configura os botões de fechar dos modais
    $('.modal .btn-close, .modal .btn-secondary').on('click', function() {
        $(this).closest('.modal').modal('hide');
    });
    
    // Configura o Select2 para o modal de contrato
    $('select[name="contrato_id"]').select2({
        dropdownParent: $('#modalContrato'),
        width: '100%',
        placeholder: 'Selecione um contrato...',
        allowClear: true,
        ajax: {
            url: '../cobranca/ajax/buscar_contratos.php',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    search: params.term,
                    page: params.page || 1,
                    processo_id: $('#formProcesso input[name="id"]').val()
                };
            },
            processResults: function(data) {
                console.log('Resultados recebidos:', data);
                return {
                    results: data.results,
                    pagination: {
                        more: data.pagination.more
                    }
                };
            },
            cache: true
        },
        minimumInputLength: 0
    });

    // Adicionar evento para recarregar a página quando o modal for fechado
    $('#modalProcesso').on('hidden.bs.modal', function (e) {
        // Salvar os filtros atuais no localStorage
        var filtros = {
            advogado: $('#filtro_advogado').val(),
            status: $('#filtro_status').val(),
            modalidade: $('#filtro_modalidade').val(),
            data_inicio: $('#filtro_data_inicio').val(),
            data_fim: $('#filtro_data_fim').val(),
            search: $('#filtro_search').val()
        };
        localStorage.setItem('processos_filtros', JSON.stringify(filtros));
        
        // Recarregar a página
        window.location.reload();
    });

    // Verificar se há filtros salvos e aplicá-los
    var filtrosSalvos = localStorage.getItem('processos_filtros');
    if (filtrosSalvos) {
        var filtros = JSON.parse(filtrosSalvos);
        $('#filtro_advogado').val(filtros.advogado);
        $('#filtro_status').val(filtros.status);
        $('#filtro_modalidade').val(filtros.modalidade);
        $('#filtro_data_inicio').val(filtros.data_inicio);
        $('#filtro_data_fim').val(filtros.data_fim);
        $('#filtro_search').val(filtros.search);
        
        // Se houver filtros salvos, submeter o formulário
        if (Object.values(filtros).some(val => val !== '')) {
            $('#formFiltros').submit();
        }
    }
});

// Função para remover ajuizamento
function removerAjuizamento(id) {
    console.log('Tentando remover ajuizamento com ID:', id);
    
    Swal.fire({
        title: 'Confirmar exclusão',
        text: 'Deseja realmente excluir este ajuizamento?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sim, excluir',
        cancelButtonText: 'Cancelar',
        confirmButtonColor: '#dc3545'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '../cobranca/ajax/excluir_ajuizamento.php',
                type: 'POST',
                data: { id: id },
                dataType: 'json',
                success: function(response) {
        if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Sucesso!',
                            text: response.message,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Erro!',
                            text: response.message || 'Erro ao excluir ajuizamento'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro na requisição:', error);
                    console.error('Status:', status);
                    console.error('Resposta:', xhr.responseText);
                    
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro!',
                        text: 'Erro ao excluir ajuizamento. Por favor, tente novamente.'
                    });
                }
            });
        }
    });
}

function removerContrato(id) {
    console.log('Tentando desvincular contrato com ID:', id);
    
    Swal.fire({
        title: 'Confirmar desvinculação',
        text: 'Deseja realmente desvincular este contrato?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sim, desvincular',
        cancelButtonText: 'Cancelar',
        confirmButtonColor: '#dc3545'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '../cobranca/ajax/remover_contrato.php',
                type: 'POST',
                data: { 
                    processo_id: <?= $id ?>,
                    contrato_id: id 
                },
                dataType: 'json',
                success: function(response) {
                if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Sucesso!',
                            text: response.message,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                        location.reload();
                    });
                } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Erro!',
                            text: response.message || 'Erro ao desvincular contrato'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro na requisição:', error);
                    console.error('Status:', status);
                    console.error('Resposta:', xhr.responseText);
                    
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro!',
                        text: 'Erro ao desvincular contrato. Por favor, tente novamente.'
                    });
                }
            });
        }
    });
}
</script> 

<style>
.modal-child {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    display: none;
}

.modal-child .modal-dialog {
    margin: 1.75rem auto;
    max-width: 800px;
    position: relative;
    width: auto;
    pointer-events: none;
}

.modal-child .modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    outline: 0;
}

.modal-child .modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: 0.3rem;
    border-top-right-radius: 0.3rem;
}

.modal-child .modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
}

.modal-child .modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 1rem;
    border-top: 1px solid #e9ecef;
    border-bottom-right-radius: 0.3rem;
    border-bottom-left-radius: 0.3rem;
}

.modal-child .select2-container {
    width: 100% !important;
}

.modal-child .form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.modal-child .form-control.is-valid {
    border-color: #198754;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000;
}
</style>

<!-- Modal de Vincular Contrato -->
<div class="modal fade modal-child" id="modalContrato" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Vincular Contrato</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="formContrato">
                    <input type="hidden" name="processo_id" value="<?= $id ?>">
                    <input type="hidden" id="valor_ajuizado" value="<?= $processo['valor_ajuizado'] ?>">
                    <input type="hidden" id="valor_total_contratos" value="<?= $valor_total_contratos ?>">
                    
                    <div class="alert alert-info">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Valor Ajuizado:</strong> <?= formatarValor($processo['valor_ajuizado']) ?>
                            </div>
                            <div>
                                <strong>Valor Disponível:</strong> <span id="valor_disponivel"><?= formatarValor($processo['valor_ajuizado'] - $valor_total_contratos) ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Buscar Contrato</label>
                                <select name="contrato_id" class="form-select" required>
                                    <option value="">Selecione um contrato...</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Valor do Contrato</label>
                                <input type="text" name="valor_contrato" id="valor_contrato" class="form-control dinheiro" required>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="salvarContrato()">Salvar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Ajuizamento -->
<div class="modal fade modal-child" id="modalAjuizamento" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Incluir Ajuizamento</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="formAjuizamento">
                    <input type="hidden" name="processo_id" value="<?= $id ?>">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Número do Processo</label>
                                <input type="text" name="numero_processo" class="form-control processo" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Data de Ajuizamento</label>
                                <input type="date" name="data_ajuizamento" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Valor Ajuizado</label>
                                <input type="text" name="valor_ajuizado" class="form-control dinheiro" required>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="salvarAjuizamento()">Salvar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Alvará -->
<div class="modal fade modal-child" id="modalAlvara" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Registrar Alvará</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="formAlvara">
                    <input type="hidden" name="processo_id" value="<?= $id ?>">
                    <input type="hidden" id="alvara_id" name="alvara_id" value="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Data do Alvará</label>
                                <input type="date" id="data_alvara" name="data_recebimento" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Valor</label>
                                <input type="text" id="valor_alvara" name="valor" class="form-control dinheiro" required>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Observações</label>
                                <textarea id="observacoes_alvara" name="observacoes" class="form-control" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="salvarAlvara()">Salvar</button>
            </div>
        </div>
    </div>
</div> 
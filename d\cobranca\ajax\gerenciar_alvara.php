<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $acao = isset($_POST['acao']) ? $_POST['acao'] : '';
    
    try {
        switch ($acao) {
            case 'incluir_alvara':
                $processo_id = intval($_POST['processo_id']);
                
                // Validar processo
                $stmt = $pdo->prepare("SELECT id FROM cbp_processos_judiciais WHERE id = ?");
                $stmt->execute([$processo_id]);
                if (!$stmt->fetch()) {
                    throw new Exception('Processo não encontrado');
                }

                // Inserir alvará
                $stmt = $pdo->prepare("
                    INSERT INTO cbp_alvaras (
                        processo_id, valor, data_recebimento, observacoes
                    ) VALUES (?, ?, ?, ?)
                ");

                $stmt->execute([
                    $processo_id,
                    str_replace(['R$', '.', ','], ['', '', '.'], $_POST['valor']),
                    $_POST['data_recebimento'],
                    $_POST['observacoes'] ?? null
                ]);

                echo json_encode([
                    'success' => true,
                    'message' => 'Alvará registrado com sucesso!'
                ]);
                break;

            default:
                throw new Exception('Ação inválida');
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Erro ao processar a ação: ' . $e->getMessage()
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Método não permitido'
    ]);
} 
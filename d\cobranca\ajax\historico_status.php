<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Validar se o processo_id foi informado
    if (!isset($_GET['processo_id']) || !is_numeric($_GET['processo_id'])) {
        throw new Exception('ID do processo não informado ou inválido');
    }

    // Buscar histórico de status do processo
    $stmt = $pdo->prepare("
        SELECT hs.*, s.nome as status_nome,
               DATE_FORMAT(hs.data_alteracao, '%d/%m/%Y') as data_formatada
        FROM cbp_historico_status hs
        LEFT JOIN cbp_status_processo s ON hs.status_id = s.id
        WHERE hs.processo_id = ?
        ORDER BY hs.data_alteracao DESC
    ");
    
    $stmt->execute([$_GET['processo_id']]);
    $historico = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'data' => $historico
    ]);

} catch (Exception $e) {
    error_log('Erro em historico_status.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao buscar histórico de status: ' . $e->getMessage()
    ]);
} 
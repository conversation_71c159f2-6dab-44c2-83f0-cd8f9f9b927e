<?php
// Desativar exibição de erros para a saída
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Verificar se a requisição é POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Método HTTP inválido!']);
    exit;
}

// Log para diagnóstico
error_log('Dados recebidos em processar_pagamento.php: ' . print_r($_POST, true));

// Incluir apenas a conexão com o banco, sem verificação de autenticação
require_once '../../config/database.php';

// Cabeçalho JSON
header('Content-Type: application/json');

// Iniciar transação
$pdo->beginTransaction();

try {
    // Validar e sanitizar dados
    $parcela_id = isset($_POST['parcela_id']) ? intval($_POST['parcela_id']) : 0;
    $acordo_id = isset($_POST['acordo_id']) ? intval($_POST['acordo_id']) : 0;
    $data_pagamento = isset($_POST['data_pagamento']) ? $_POST['data_pagamento'] : null;
    $valor_pago = isset($_POST['valor_pago']) ? str_replace(',', '.', $_POST['valor_pago']) : 0;
    $observacoes = isset($_POST['observacoes']) ? trim($_POST['observacoes']) : '';
    
    error_log("ID da parcela: $parcela_id, ID do acordo: $acordo_id, Valor: $valor_pago");
    
    // Validações iniciais
    if (!$parcela_id || !$acordo_id) {
        throw new Exception('Parâmetros inválidos');
    }

    // Verificar se o acordo está ativo
    $stmt = $pdo->prepare("SELECT ativo FROM cbp_acordos WHERE id = ?");
    $stmt->execute([$acordo_id]);
    $acordo_ativo = $stmt->fetchColumn();

    if (!$acordo_ativo) {
        throw new Exception("Não é possível registrar pagamentos em acordos inativos.");
    }
    
    // Validação adicional do valor
    $valor_pago = filter_var($valor_pago, FILTER_VALIDATE_FLOAT);
    if ($valor_pago === false) {
        throw new Exception('Valor pago inválido!');
    }

    if (!$data_pagamento) {
        throw new Exception('Data de pagamento é obrigatória');
    }

    if ($valor_pago <= 0) {
        throw new Exception('Valor pago deve ser maior que zero');
    }

    // Verificar se a parcela existe e está pendente
    $stmt = $pdo->prepare("
        SELECT * FROM cbp_parcelas_acordo
        WHERE id = ? AND acordo_id = ?
    ");
    $stmt->execute([$parcela_id, $acordo_id]);
    $parcela = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$parcela) {
        throw new Exception('Parcela não encontrada');
    }

    error_log("Status da parcela: " . $parcela['status']);
    
    if ($parcela['status'] == 'PAGO') {
        throw new Exception('Esta parcela já foi paga');
    }

    // Atualizar parcela
    $stmt = $pdo->prepare("
        UPDATE cbp_parcelas_acordo
        SET status = 'PAGO',
            data_pagamento = ?,
            valor_pago = ?,
            observacoes = ?,
            updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$data_pagamento, $valor_pago, $observacoes, $parcela_id]);

    // Registrar honorário usando debug_honorarios.php
    try {
        error_log("Executando arquivo debug_honorarios.php para registrar honorário");
        $parcela_id = $parcela['id']; // Define o ID da parcela que acabou de ser paga
        include_once(dirname(__DIR__) . '/debug_honorarios.php');
        error_log("Arquivo debug_honorarios.php executado com sucesso");
    } catch (Exception $e) {
        error_log("Erro ao executar debug_honorarios.php: " . $e->getMessage());
    }

    // Verificar se todas as parcelas foram pagas
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total,
               SUM(CASE WHEN status = 'PAGO' THEN 1 ELSE 0 END) as pagas,
               EXISTS (
                   SELECT 1 
                   FROM cbp_parcelas_acordo 
                   WHERE acordo_id = ? 
                   AND status = 'PENDENTE' 
                   AND data_vencimento < CURRENT_DATE
               ) as tem_atrasadas
        FROM cbp_parcelas_acordo
        WHERE acordo_id = ?
    ");
    $stmt->execute([$acordo_id, $acordo_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    // Buscar IDs dos status
    $stmt = $pdo->prepare("SELECT id, nome FROM cbp_status_acordo");
    $stmt->execute();
    $status_acordo = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $status_acordo[$row['nome']] = $row['id'];
    }

    // Verificar status atual do processo
    $stmt = $pdo->prepare("SELECT status_id FROM cbp_processos_judiciais WHERE id = ?");
    $stmt->execute([$processo_id]);
    $status_atual_processo = $stmt->fetchColumn();
    error_log("Status atual do processo: " . $status_atual_processo);

    // Verificar acordos ativos do processo
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total,
               SUM(CASE WHEN ativo = 1 THEN 1 ELSE 0 END) as ativos
        FROM cbp_acordos 
        WHERE processo_id = ?
    ");
    $stmt->execute([$processo_id]);
    $info_acordos = $stmt->fetch(PDO::FETCH_ASSOC);
    error_log("Total de acordos: " . $info_acordos['total'] . ", Ativos: " . $info_acordos['ativos']);

    // Determinar novo status do acordo
    $novo_status_id = null;
    
    // Verificar se tem alvarás pendentes
    $stmt = $pdo->prepare("
        SELECT EXISTS (
            SELECT 1
            FROM cbp_alvaras_acordo aa
            WHERE aa.acordo_id = ?
            AND aa.status = 'PENDENTE'
        ) as tem_alvara_pendente
    ");
    $stmt->execute([$acordo_id]);
    $tem_alvara_pendente = $stmt->fetchColumn();

    if ($result['total'] == $result['pagas'] && !$tem_alvara_pendente) {
        // Todas as parcelas pagas e sem alvarás pendentes - QUITADO
        $novo_status_id = $status_acordo['QUITADO'] ?? null;
    } elseif ($result['tem_atrasadas']) {
        // Tem parcelas em atraso - INADIMPLENTE
        $novo_status_id = $status_acordo['INADIMPLENTE'] ?? null;
    } else {
        // Pagamento normal - VIGENTE
        $novo_status_id = $status_acordo['VIGENTE'] ?? null;
    }

    // Atualizar status do acordo
    if ($novo_status_id) {
        $stmt = $pdo->prepare("
            UPDATE cbp_acordos
            SET status_id = ?,
                updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$novo_status_id, $acordo_id]);

        // Se o acordo foi quitado, verificar se todas as parcelas de todos os acordos deste processo estão quitadas
        if ($result['total'] == $result['pagas'] && !$tem_alvara_pendente && isset($status_acordo['QUITADO'])) {
            // Buscar processo do acordo
            $stmt = $pdo->prepare("SELECT processo_id FROM cbp_acordos WHERE id = ?");
            $stmt->execute([$acordo_id]);
            $processo_id = $stmt->fetchColumn();

            if ($processo_id) {
                // Verificar se existem outros acordos ativos não quitados
                $stmt = $pdo->prepare("
                    SELECT a.id, a.ativo,
                           (SELECT COUNT(*) FROM cbp_parcelas_acordo pa WHERE pa.acordo_id = a.id AND pa.status != 'PAGO') as parcelas_pendentes,
                           (SELECT COUNT(*) FROM cbp_alvaras_acordo aa WHERE aa.acordo_id = a.id AND aa.status = 'PENDENTE') as alvaras_pendentes
                    FROM cbp_acordos a
                    WHERE a.processo_id = ?
                    AND a.id != ?
                    AND a.ativo = 1
                ");
                $stmt->execute([$processo_id, $acordo_id]);
                $outros_acordos = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $outros_acordos_nao_quitados = false;
                foreach ($outros_acordos as $acordo) {
                    error_log("Acordo ID: " . $acordo['id'] . 
                             " - Parcelas pendentes: " . $acordo['parcelas_pendentes'] . 
                             " - Alvarás pendentes: " . $acordo['alvaras_pendentes']);
                    
                    if ($acordo['parcelas_pendentes'] > 0 || $acordo['alvaras_pendentes'] > 0) {
                        $outros_acordos_nao_quitados = true;
                        break;
                    }
                }

                // Se não houver outros acordos ativos não quitados, atualizar status do processo para QUITADO
                if (!$outros_acordos_nao_quitados) {
                    error_log("Tentando atualizar status do processo para QUITADO");
                    // Buscar ID do status QUITADO para processo
                    $stmt = $pdo->prepare("SELECT id FROM cbp_status_processo WHERE nome = 'QUITADO'");
                    $stmt->execute();
                    $status_quitado_id = $stmt->fetchColumn();
                    error_log("ID do status QUITADO: " . ($status_quitado_id ?: 'não encontrado'));

                    if ($status_quitado_id) {
                        error_log("Atualizando status do processo para QUITADO");
                        $stmt = $pdo->prepare("
                            UPDATE cbp_processos_judiciais 
                            SET status_id = ?,
                                updated_at = NOW() 
                            WHERE id = ?
                        ");
                        $stmt->execute([$status_quitado_id, $processo_id]);
                        error_log("Status do processo atualizado. Rows afetadas: " . $stmt->rowCount());

                        // Registrar no histórico de status
                        $stmt = $pdo->prepare("
                            INSERT INTO cbp_historico_status 
                            (processo_id, status_id, data_alteracao, observacoes) 
                            VALUES (?, ?, NOW(), ?)
                        ");
                        $stmt->execute([
                            $processo_id,
                            $status_quitado_id,
                            'Processo quitado automaticamente após quitação de todos os acordos'
                        ]);
                        error_log("Histórico de status registrado");

                        // Verificar se o status foi realmente atualizado
                        $stmt = $pdo->prepare("SELECT status_id FROM cbp_processos_judiciais WHERE id = ?");
                        $stmt->execute([$processo_id]);
                        $status_final = $stmt->fetchColumn();
                        error_log("Status final do processo após atualização: " . $status_final);
                    }
                }
            }
        }
    }

    // Commit
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Pagamento registrado com sucesso!'
    ]);
} catch (Exception $e) {
    $pdo->rollBack();
    error_log("Erro ao processar pagamento: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?> 
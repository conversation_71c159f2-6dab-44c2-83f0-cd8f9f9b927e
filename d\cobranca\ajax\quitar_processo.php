<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Verificar se o ID foi fornecido
    if (!isset($_POST['id']) || empty($_POST['id'])) {
        throw new Exception('ID do processo não fornecido');
    }

    $processo_id = intval($_POST['id']);

    // Iniciar transação
    $pdo->beginTransaction();

    // Buscar o status QUITADO (assumindo que o ID é 3 baseado no código anterior)
    $status_quitado_id = 3;

    // Atualizar o status do processo para QUITADO
    $stmt = $pdo->prepare("UPDATE cbp_processos_judiciais SET status_id = ? WHERE id = ?");
    if (!$stmt->execute([$status_quitado_id, $processo_id])) {
        throw new Exception('Erro ao atualizar o status do processo');
    }

    // Inativar acordos existentes
    $stmt = $pdo->prepare("UPDATE cbp_acordos SET ativo = 0 WHERE processo_id = ? AND ativo = 1");
    if (!$stmt->execute([$processo_id])) {
        throw new Exception('Erro ao inativar acordos existentes');
    }

    // Commit da transação
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Processo quitado com sucesso!'
    ]);

} catch (Exception $e) {
    // Rollback em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    echo json_encode([
        'success' => false,
        'message' => 'Erro ao quitar processo: ' . $e->getMessage()
    ]);
} 
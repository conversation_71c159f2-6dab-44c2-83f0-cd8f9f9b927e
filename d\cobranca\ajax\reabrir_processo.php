<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Verificar se o ID foi fornecido
    if (!isset($_POST['id']) || empty($_POST['id'])) {
        throw new Exception('ID do processo não fornecido');
    }

    $processo_id = intval($_POST['id']);

    // Iniciar transação
    $pdo->beginTransaction();

    // Buscar o status VIGENTE (assumindo que o ID é 1 baseado no código anterior)
    $status_vigente_id = 1;

    // Buscar o último acordo que estava ativo antes da quitação
    // Ordenamos por data_acordo DESC para pegar o mais recente
    $stmt = $pdo->prepare("
        SELECT id 
        FROM cbp_acordos 
        WHERE processo_id = ? 
        ORDER BY data_acordo DESC, id DESC 
        LIMIT 1
    ");
    $stmt->execute([$processo_id]);
    $ultimo_acordo = $stmt->fetch(PDO::FETCH_ASSOC);

    // Se encontrou um acordo, reativa ele
    if ($ultimo_acordo) {
        $stmt = $pdo->prepare("UPDATE cbp_acordos SET ativo = 1 WHERE id = ?");
        if (!$stmt->execute([$ultimo_acordo['id']])) {
            throw new Exception('Erro ao reativar o acordo');
        }

        // Como tem acordo ativo, mudamos o status para ACORDO JUDICIAL (ID 2)
        $status_vigente_id = 2;
    }

    // Atualizar o status do processo
    $stmt = $pdo->prepare("UPDATE cbp_processos_judiciais SET status_id = ? WHERE id = ?");
    if (!$stmt->execute([$status_vigente_id, $processo_id])) {
        throw new Exception('Erro ao atualizar o status do processo');
    }

    // Commit da transação
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Processo reaberto com sucesso!' . 
                    ($ultimo_acordo ? ' O último acordo foi reativado.' : '')
    ]);

} catch (Exception $e) {
    // Rollback em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    echo json_encode([
        'success' => false,
        'message' => 'Erro ao reabrir processo: ' . $e->getMessage()
    ]);
} 
<?php
// Iniciar sessão para garantir que auth_check.php funcione corretamente
// session_start(); // Removido para evitar avisos, pois já é chamado em auth_check.php

// Verificar se a requisição é POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método HTTP inválido!']);
    exit;
}

// Log para diagnóstico
error_log('Iniciando registro de pagamento...');
error_log('POST data: ' . print_r($_POST, true));

require_once '../auth_check.php';
require_once '../config/database.php';
// Removido a importação de honorarios_hooks.php, pois não será mais usado

// Iniciar transação
$pdo->beginTransaction();

try {
    // Validar e sanitizar dados
    $parcela_id = isset($_POST['parcela_id']) ? intval($_POST['parcela_id']) : 0;
    $acordo_id = isset($_POST['acordo_id']) ? intval($_POST['acordo_id']) : 0;
    $data_pagamento = isset($_POST['data_pagamento']) ? $_POST['data_pagamento'] : null;
    $valor_pago = isset($_POST['valor_pago']) ? str_replace(',', '.', $_POST['valor_pago']) : 0;
    $observacoes = isset($_POST['observacoes']) ? trim($_POST['observacoes']) : '';
    
    error_log("=== INÍCIO DO REGISTRO DE PAGAMENTO ===");
    error_log("Dados recebidos:");
    error_log("- Parcela ID: $parcela_id");
    error_log("- Acordo ID: $acordo_id");
    error_log("- Data pagamento: $data_pagamento");
    error_log("- Valor pago: $valor_pago");
    
    // Validação adicional do valor
    $valor_pago = filter_var($valor_pago, FILTER_VALIDATE_FLOAT);
    if ($valor_pago === false) {
        throw new Exception('Valor pago inválido!');
    }

    // Validações
    if (!$parcela_id || !$acordo_id) {
        throw new Exception('Parâmetros inválidos');
    }

    if (!$data_pagamento) {
        throw new Exception('Data de pagamento é obrigatória');
    }

    if ($valor_pago <= 0) {
        throw new Exception('Valor pago deve ser maior que zero');
    }

    // Verificar se a parcela existe e está pendente
    $stmt = $pdo->prepare("
        SELECT p.*, a.processo_id, a.quantidade_parcelas
        FROM cbp_parcelas_acordo p
        INNER JOIN cbp_acordos a ON p.acordo_id = a.id
        WHERE p.id = ? AND p.acordo_id = ?
    ");
    $stmt->execute([$parcela_id, $acordo_id]);
    $parcela = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$parcela) {
        throw new Exception('Parcela não encontrada');
    }

    error_log("Dados da parcela encontrados:");
    error_log(print_r($parcela, true));
    
    if ($parcela['status'] == 'PAGO') {
        throw new Exception('Esta parcela já foi paga');
    }

    // Atualizar parcela
    $stmt = $pdo->prepare("
        UPDATE cbp_parcelas_acordo
        SET status = 'PAGO',
            data_pagamento = ?,
            valor_pago = ?,
            observacoes = ?,
            updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$data_pagamento, $valor_pago, $observacoes, $parcela_id]);
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('Falha ao atualizar o status da parcela');
    }

    error_log("Parcela atualizada com sucesso");

    // Verificar se todas as parcelas foram pagas e se há alvarás pendentes
    $stmt = $pdo->prepare("
        SELECT 
            (SELECT COUNT(*) FROM cbp_parcelas_acordo WHERE acordo_id = ?) as total_parcelas,
            (SELECT COUNT(*) FROM cbp_parcelas_acordo WHERE acordo_id = ? AND status = 'PAGO') as parcelas_pagas,
            EXISTS (
                SELECT 1 
                FROM cbp_parcelas_acordo 
                WHERE acordo_id = ? 
                AND status = 'PENDENTE' 
                AND data_vencimento < CURRENT_DATE
            ) as tem_atrasadas,
            EXISTS (
                SELECT 1
                FROM cbp_alvaras_acordo
                WHERE acordo_id = ?
                AND status = 'PENDENTE'
            ) as tem_alvara_pendente
        FROM dual
    ");
    $stmt->execute([$acordo_id, $acordo_id, $acordo_id, $acordo_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    // Buscar IDs dos status
    $stmt = $pdo->prepare("SELECT id, nome FROM cbp_status_acordo");
    $stmt->execute();
    $status_acordo = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $status_acordo[$row['nome']] = $row['id'];
    }

    // Determinar novo status do acordo
    $novo_status_id = null;
    if ($result['total_parcelas'] == $result['parcelas_pagas'] && !$result['tem_alvara_pendente']) {
        // Todas as parcelas pagas e sem alvarás pendentes - QUITADO
        $novo_status_id = $status_acordo['QUITADO'] ?? null;
    } elseif ($result['tem_atrasadas']) {
        // Tem parcelas em atraso - INADIMPLENTE
        $novo_status_id = $status_acordo['INADIMPLENTE'] ?? null;
    } else {
        // Pagamento normal - VIGENTE
        $novo_status_id = $status_acordo['VIGENTE'] ?? null;
    }

    // Atualizar status do acordo
    if ($novo_status_id) {
        $stmt = $pdo->prepare("
            UPDATE cbp_acordos
            SET status_id = ?,
                updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$novo_status_id, $acordo_id]);

        // Se o acordo foi quitado, verificar se todas as parcelas de todos os acordos deste processo estão quitadas
        if ($result['total_parcelas'] == $result['parcelas_pagas'] && !$result['tem_alvara_pendente'] && isset($status_acordo['QUITADO'])) {
            // Buscar processo do acordo
            $stmt = $pdo->prepare("SELECT processo_id FROM cbp_acordos WHERE id = ?");
            $stmt->execute([$acordo_id]);
            $processo_id = $stmt->fetchColumn();

            if ($processo_id) {
                // Verificar se todos os acordos ativos do processo estão quitados
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) FROM cbp_acordos
                    WHERE processo_id = ?
                    AND ativo = 1
                    AND status_id != ?
                ");
                $stmt->execute([$processo_id, $status_acordo['QUITADO']]);
                $acordos_nao_quitados = $stmt->fetchColumn();

                // Se todos os acordos ativos estiverem quitados, atualizar status do processo para QUITADO
                if ($acordos_nao_quitados == 0) {
                    // Buscar ID do status QUITADO para processo
                    $stmt = $pdo->prepare("SELECT id FROM cbp_status_processo WHERE nome = 'QUITADO'");
                    $stmt->execute();
                    $status_quitado_id = $stmt->fetchColumn();

                    if ($status_quitado_id) {
                        $stmt = $pdo->prepare("
                            UPDATE cbp_processos_judiciais
                            SET status_id = ?,
                                updated_at = NOW()
                            WHERE id = ?
                        ");
                        $stmt->execute([$status_quitado_id, $processo_id]);
                    }
                }
            }
        }
    }

    // Commit da transação principal
    $pdo->commit();
    error_log("=== FIM DO REGISTRO DE PAGAMENTO - SUCESSO ===");

    // Executar o debug_honorarios.php de forma direta
    try {
        error_log("Executando arquivo debug_honorarios.php");
        include_once(dirname(__DIR__) . '/debug_honorarios.php');
        error_log("Arquivo debug_honorarios.php executado com sucesso");
    } catch (Exception $e) {
        error_log("Erro ao executar debug_honorarios.php: " . $e->getMessage());
    }

    echo json_encode([
        'success' => true,
        'message' => 'Pagamento registrado com sucesso!'
    ]);
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("=== ERRO NO REGISTRO DE PAGAMENTO ===");
    error_log("Mensagem: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("=== FIM DO ERRO ===");
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'details' => 'Ocorreu um erro ao registrar o pagamento. Por favor, tente novamente.'
    ]);
}
?> 
<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Validar se recebeu os dados via POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    // Verificar se o usuário tem permissão de GESTOR
    if (TIPO_ACESSO_COBRANCA !== 'GESTOR') {
        throw new Exception('Acesso negado. Apenas gestores podem remover informações de ajuizamento.');
    }

    // Validar campos obrigatórios
    if (!isset($_POST['processo_id']) || empty($_POST['processo_id'])) {
        throw new Exception('ID do processo não informado');
    }

    $processo_id = intval($_POST['processo_id']);

    // Verificar se o processo existe
    $stmt = $pdo->prepare("SELECT id FROM cbp_processos_judiciais WHERE id = ?");
    $stmt->execute([$processo_id]);
    if (!$stmt->fetch()) {
        throw new Exception('Processo não encontrado');
    }

    // Verificar se tem acordos
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_acordos WHERE processo_id = ?");
    $stmt->execute([$processo_id]);
    $tem_acordos = $stmt->fetchColumn() > 0;
    
    // Verificar se tem alvarás
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_alvaras WHERE processo_id = ?");
    $stmt->execute([$processo_id]);
    $tem_alvaras = $stmt->fetchColumn() > 0;
    
    // Se tiver acordos ou alvarás, não permitir a remoção
    if ($tem_acordos || $tem_alvaras) {
        throw new Exception('Não é possível remover as informações de ajuizamento deste processo pois ele possui acordos ou alvarás cadastrados.');
    }

    $pdo->beginTransaction();

    // Buscar informações do processo antes de remover o ajuizamento
    $stmt = $pdo->prepare("SELECT numero_processo, data_ajuizamento, valor_ajuizado, vara, comarca 
                          FROM cbp_processos_judiciais 
                          WHERE id = ?");
    $stmt->execute([$processo_id]);
    $processo = $stmt->fetch();

    if (!$processo) {
        throw new Exception('Processo não encontrado');
    }

    // Atualizar processo
    $stmt = $pdo->prepare("
        UPDATE cbp_processos_judiciais 
        SET data_ajuizamento = NULL, 
            valor_ajuizado = NULL,
            updated_at = NOW()
        WHERE id = ?
    ");

    $stmt->execute([$processo_id]);

    // Registrar no log
    $detalhes = "Remoção de ajuizamento - Processo ID: " . $processo_id . 
                " - Nº Processo: " . $processo['numero_processo'] . 
                " - Data Ajuizamento: " . date('d/m/Y', strtotime($processo['data_ajuizamento'])) . 
                " - Valor: R$ " . number_format($processo['valor_ajuizado'], 2, ',', '.') . 
                " - Vara: " . $processo['vara'] . 
                " - Comarca: " . $processo['comarca'];
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Remoção de Ajuizamento', $detalhes]);

    $pdo->commit();
    echo json_encode(['success' => true, 'message' => 'Ajuizamento removido com sucesso']);
} catch (Exception $e) {
    $pdo->rollBack();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} 
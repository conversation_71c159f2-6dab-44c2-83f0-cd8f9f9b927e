<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Verificar se é uma requisição POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    // Validar campos obrigatórios
    $campos_obrigatorios = ['nome', 'cpf_cnpj', 'pa_id'];
    foreach ($campos_obrigatorios as $campo) {
        if (!isset($_POST[$campo]) || empty($_POST[$campo])) {
            throw new Exception("O campo {$campo} é obrigatório");
        }
    }

    // Se for uma edição (ID fornecido)
    if (!empty($_POST['id'])) {
        $id = (int)$_POST['id'];
        
        // Buscar dados atuais do associado
        $stmt = $pdo->prepare("SELECT nome, cpf_cnpj FROM cbp_associados WHERE id = ?");
        $stmt->execute([$id]);
        $associado_atual = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$associado_atual) {
            throw new Exception('Associado não encontrado');
        }

        // Se não for GESTOR, verificar se houve tentativa de alteração do nome ou CPF/CNPJ
        if (TIPO_ACESSO_COBRANCA !== 'GESTOR') {
            if ($_POST['nome'] !== $associado_atual['nome'] || $_POST['cpf_cnpj'] !== $associado_atual['cpf_cnpj']) {
                throw new Exception('Apenas gestores podem alterar o nome e CPF/CNPJ do associado');
            }
        }
    }

    // Preparar os dados
    $dados = [
        'nome' => trim($_POST['nome']),
        'cpf_cnpj' => preg_replace('/[^0-9]/', '', $_POST['cpf_cnpj']),
        'pa_id' => (int)$_POST['pa_id'],
        'numero_contato' => $_POST['numero_contato'] ?? null,
        'ativo' => isset($_POST['ativo']) ? (int)$_POST['ativo'] : 1
    ];

    // Validar se o PA existe
    $stmt = $pdo->prepare("SELECT id FROM pontos_atendimento WHERE id = ?");
    $stmt->execute([$dados['pa_id']]);
    if (!$stmt->fetch()) {
        throw new Exception('Ponto de Atendimento não encontrado');
    }

    // Se for uma edição
    if (!empty($_POST['id'])) {
        $id = (int)$_POST['id'];
        
        $sql = "UPDATE cbp_associados SET 
                nome = ?, 
                cpf_cnpj = ?, 
                pa_id = ?, 
                numero_contato = ?,
                ativo = ?,
                updated_at = NOW()
                WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $dados['nome'],
            $dados['cpf_cnpj'],
            $dados['pa_id'],
            $dados['numero_contato'],
            $dados['ativo'],
            $id
        ]);

        // Registrar no log
        $detalhes = "Atualização de associado - ID: " . $id . 
                    " - Nome: " . $dados['nome'] . 
                    " - CPF/CNPJ: " . $dados['cpf_cnpj'] . 
                    " - PA: " . $dados['pa_id'] . 
                    " - Status: " . ($dados['ativo'] ? "Ativo" : "Inativo");
        $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
        $stmt->execute([$_SESSION['user_id'], 'Atualização de Associado', $detalhes]);

        $mensagem = 'Associado atualizado com sucesso';
    }
    // Se for um novo cadastro
    else {
        $sql = "INSERT INTO cbp_associados 
                (nome, cpf_cnpj, pa_id, numero_contato, ativo, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, NOW(), NOW())";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $dados['nome'],
            $dados['cpf_cnpj'],
            $dados['pa_id'],
            $dados['numero_contato'],
            $dados['ativo']
        ]);

        // Registrar no log
        $associado_id = $pdo->lastInsertId();
        $detalhes = "Criação de associado - ID: " . $associado_id . 
                    " - Nome: " . $dados['nome'] . 
                    " - CPF/CNPJ: " . $dados['cpf_cnpj'] . 
                    " - PA: " . $dados['pa_id'] . 
                    " - Status: " . ($dados['ativo'] ? "Ativo" : "Inativo");
        $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
        $stmt->execute([$_SESSION['user_id'], 'Criação de Associado', $detalhes]);

        $mensagem = 'Associado cadastrado com sucesso';
    }

    echo json_encode([
        'success' => true,
        'message' => $mensagem
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 
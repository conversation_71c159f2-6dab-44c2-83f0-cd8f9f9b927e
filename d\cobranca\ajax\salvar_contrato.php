<?php
// Habilitar exibição de erros para debug (remover em produção)
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

// Definir que a resposta será um JSON
header('Content-Type: application/json');

require_once '../../auth_check.php';
require_once '../../config/database.php';

// Verificar se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit;
}

// Validar campos obrigatórios
$required_fields = ['associado_id', 'numero_contrato', 'modalidade_id'];
foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || empty($_POST[$field])) {
        echo json_encode(['success' => false, 'message' => "Campo ".ucfirst(str_replace('_', ' ', $field))." é obrigatório"]);
        exit;
    }
}

try {
    // Iniciar transação
    $pdo->beginTransaction();

    // Verificar se o associado existe
    $stmt = $pdo->prepare("SELECT id FROM cbp_associados WHERE id = ?");
    $stmt->execute([$_POST['associado_id']]);
    if (!$stmt->fetch()) {
        throw new Exception('Associado não encontrado');
    }

    // Verificar se é inserção ou atualização
    if (isset($_POST['id']) && !empty($_POST['id'])) {
        // ATUALIZAÇÃO
        $id = (int)$_POST['id'];
        
        // Verificar se o contrato existe e obter associado antigo
        $stmt = $pdo->prepare("SELECT id, associado_id FROM cbp_contratos WHERE id = ?");
        $stmt->execute([$id]);
        $contrato_atual = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$contrato_atual) {
            throw new Exception('Contrato não encontrado');
        }
        
        // Verificar se o contrato já está vinculado a algum processo
        $stmt = $pdo->prepare("SELECT id FROM cbp_processos_contratos WHERE contrato_id = ?");
        $stmt->execute([$id]);
        $vinculado = $stmt->fetch() ? true : false;
        
        // Se vinculado, impedir alterações no número do contrato
        if ($vinculado) {
            $stmt = $pdo->prepare("SELECT numero_contrato FROM cbp_contratos WHERE id = ?");
            $stmt->execute([$id]);
            $contrato_atual = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($contrato_atual['numero_contrato'] != $_POST['numero_contrato']) {
                throw new Exception('Não é possível alterar o número de contrato quando este já está vinculado a um processo');
            }
        }
        
        // Atualizar contrato
        $stmt = $pdo->prepare("
            UPDATE cbp_contratos SET 
                associado_id = ?,
                numero_contrato = ?, 
                modalidade_id = ?,
                possui_garantia_real = ?,
                tipo_garantia = ?,
                descricao_bem = ?,
                data_envio = ?,
                advogado_id = ?,
                updated_at = NOW()
            WHERE id = ?
        ");
        
        $possui_garantia = isset($_POST['possui_garantia_real']) ? 1 : 0;
        $tipo_garantia = ($possui_garantia && isset($_POST['tipo_garantia'])) ? $_POST['tipo_garantia'] : null;
        $descricao_bem = ($possui_garantia && isset($_POST['descricao_bem'])) ? $_POST['descricao_bem'] : null;
        
        $stmt->execute([
            $_POST['associado_id'],
            $_POST['numero_contrato'],
            $_POST['modalidade_id'],
            $possui_garantia,
            $tipo_garantia,
            $descricao_bem,
            $_POST['data_envio'],
            $_POST['advogado_id'],
            $id
        ]);
        
        // Registrar no log
        $detalhes = "Atualização de contrato - ID: " . $id . 
                    " - Nº Contrato: " . $_POST['numero_contrato'] . 
                    " - Associado ID: " . $_POST['associado_id'] . 
                    " - Valor: R$ " . number_format($_POST['valor_contratado'], 2, ',', '.') . 
                    " - Status: " . ($_POST['ativo'] ? "Ativo" : "Inativo");
        $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
        $stmt->execute([$_SESSION['user_id'], 'Atualização de Contrato', $detalhes]);
        
        $message = 'Contrato atualizado com sucesso';
    } else {
        // INSERÇÃO
        // Verificar se já existe contrato com mesmo número para o mesmo associado
        $stmt = $pdo->prepare("SELECT id FROM cbp_contratos WHERE numero_contrato = ? AND associado_id = ?");
        $stmt->execute([$_POST['numero_contrato'], $_POST['associado_id']]);
        if ($stmt->fetch()) {
            throw new Exception('Já existe um contrato com este número para este associado');
        }
        
        // Inserir novo contrato
        $stmt = $pdo->prepare("
            INSERT INTO cbp_contratos (
                associado_id,
                numero_contrato,
                modalidade_id,
                possui_garantia_real,
                tipo_garantia,
                descricao_bem,
                data_envio,
                advogado_id,
                created_at,
                updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $possui_garantia = isset($_POST['possui_garantia_real']) ? 1 : 0;
        $tipo_garantia = ($possui_garantia && isset($_POST['tipo_garantia'])) ? $_POST['tipo_garantia'] : null;
        $descricao_bem = ($possui_garantia && isset($_POST['descricao_bem'])) ? $_POST['descricao_bem'] : null;
        
        $stmt->execute([
            $_POST['associado_id'],
            $_POST['numero_contrato'],
            $_POST['modalidade_id'],
            $possui_garantia,
            $tipo_garantia,
            $descricao_bem,
            $_POST['data_envio'],
            $_POST['advogado_id']
        ]);
        
        // Registrar no log
        $contrato_id = $pdo->lastInsertId();
        $detalhes = "Criação de contrato - ID: " . $contrato_id . 
                    " - Nº Contrato: " . $_POST['numero_contrato'] . 
                    " - Associado ID: " . $_POST['associado_id'];

        // Adicionar valor ao log apenas se existir
        if (isset($_POST['valor_contratado']) && !empty($_POST['valor_contratado'])) {
            $valor = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['valor_contratado']);
            if (is_numeric($valor)) {
                $detalhes .= " - Valor: R$ " . number_format(floatval($valor), 2, ',', '.');
            }
        }

        // Adicionar status ao log apenas se existir
        if (isset($_POST['ativo'])) {
            $detalhes .= " - Status: " . ($_POST['ativo'] ? "Ativo" : "Inativo");
        }

        $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
        $stmt->execute([$_SESSION['user_id'], 'Criação de Contrato', $detalhes]);
        
        $id = $pdo->lastInsertId();
        $message = 'Contrato cadastrado com sucesso';
    }
    
    // Commit da transação
    $pdo->commit();
    
    // Retornar sucesso
    echo json_encode([
        'success' => true,
        'message' => $message,
        'id' => $id
    ]);
    
} catch (Exception $e) {
    // Rollback em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    // Retornar erro
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 
<?php
require_once '../../config/database.php';

try {
    // Verificar se o processo_id foi fornecido
    if (!isset($_GET['processo_id'])) {
        throw new Exception('ID do processo não fornecido');
    }

    $processo_id = intval($_GET['processo_id']);

    // Buscar acordo vigente
    $stmt = $pdo->prepare("
        SELECT a.id, a.numero_repactuacao
        FROM cbp_acordos a 
        INNER JOIN cbp_status_acordo sa ON a.status_id = sa.id 
        WHERE a.processo_id = ? 
        AND sa.nome IN ('VIGENTE', 'INADIMPLENTE')
        AND a.ativo = 1
    ");
    $stmt->execute([$processo_id]);
    $acordo_vigente = $stmt->fetch(PDO::FETCH_ASSOC);

    // Garantir que o cabeçalho seja JSON
    header('Content-Type: application/json');

    // Verificar se existe acordo vigente
    $tem_acordo_vigente = ($acordo_vigente !== false);

    echo json_encode([
        'success' => true,
        'tem_acordo_vigente' => $tem_acordo_vigente,
        'acordo_vigente' => $acordo_vigente
    ]);

} catch (Exception $e) {
    header('Content-Type: application/json');
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 
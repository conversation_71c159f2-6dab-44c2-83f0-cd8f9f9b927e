<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    if (!isset($_POST['processo_id'])) {
        throw new Exception('ID do processo não fornecido');
    }

    $processo_id = intval($_POST['processo_id']);
    
    $pdo->beginTransaction();

    // Verificar status atual do processo
    $stmt = $pdo->prepare("
        SELECT p.status_id, p.id, s.nome as status_nome
        FROM cbp_processos_judiciais p
        LEFT JOIN cbp_status_processo s ON p.status_id = s.id
        WHERE p.id = ?
    ");
    $stmt->execute([$processo_id]);
    $processo = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$processo) {
        throw new Exception('Processo não encontrado');
    }

    // Verificar acordos ativos e seus status
    $stmt = $pdo->prepare("
        SELECT a.id, a.ativo,
               (SELECT COUNT(*) FROM cbp_parcelas_acordo pa WHERE pa.acordo_id = a.id AND pa.status != 'PAGO') as parcelas_pendentes,
               (SELECT COUNT(*) FROM cbp_alvaras_acordo aa WHERE aa.acordo_id = a.id AND aa.status = 'PENDENTE') as alvaras_pendentes
        FROM cbp_acordos a
        WHERE a.processo_id = ?
        AND a.ativo = 1
    ");
    $stmt->execute([$processo_id]);
    $acordos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Verificar se todos os acordos ativos estão quitados
    $todos_acordos_quitados = true;
    foreach ($acordos as $acordo) {
        if ($acordo['parcelas_pendentes'] > 0 || $acordo['alvaras_pendentes'] > 0) {
            $todos_acordos_quitados = false;
            break;
        }
    }

    // Se todos os acordos ativos estiverem quitados, atualizar status do processo para QUITADO
    if ($todos_acordos_quitados && count($acordos) > 0) {
        // Buscar ID do status QUITADO
        $stmt = $pdo->prepare("SELECT id FROM cbp_status_processo WHERE nome = 'QUITADO'");
        $stmt->execute();
        $status_quitado_id = $stmt->fetchColumn();

        if ($status_quitado_id && $processo['status_id'] != $status_quitado_id) {
            // Atualizar status do processo
            $stmt = $pdo->prepare("
                UPDATE cbp_processos_judiciais 
                SET status_id = ?,
                    updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$status_quitado_id, $processo_id]);

            // Registrar no histórico
            $stmt = $pdo->prepare("
                INSERT INTO cbp_historico_status 
                (processo_id, status_id, data_alteracao, observacoes) 
                VALUES (?, ?, NOW(), ?)
            ");
            $stmt->execute([
                $processo_id,
                $status_quitado_id,
                'Status atualizado automaticamente para QUITADO'
            ]);

            $pdo->commit();

            echo json_encode([
                'success' => true,
                'novo_status' => true
            ]);
            exit;
        }
    }
    
    $pdo->commit();
    echo json_encode([
        'success' => true,
        'novo_status' => false
    ]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 
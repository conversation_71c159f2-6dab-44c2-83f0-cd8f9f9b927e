<?php
// Não colocamos headers aqui pois este arquivo é incluído em outros
if (!isset($pdo)) {
    require_once '../../config/database.php';
}

try {
    // Buscar ID do status QUITADO
    $stmt = $pdo->prepare("SELECT id FROM cbp_status_processo WHERE nome = 'QUITADO'");
    $stmt->execute();
    $status_quitado_id = $stmt->fetchColumn();

    if (!$status_quitado_id) {
        // Registrar erro mas não interromper a execução
        error_log('Status QUITADO não encontrado');
        return;
    }

    // Buscar todos os processos que têm acordos ativos que não estão com status QUITADO
    $stmt = $pdo->prepare("
        SELECT DISTINCT p.id
        FROM cbp_processos_judiciais p
        INNER JOIN cbp_acordos a ON a.processo_id = p.id
        WHERE a.ativo = 1
        AND p.status_id != ?
    ");
    $stmt->execute([$status_quitado_id]);
    $processos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($processos as $processo) {
        // Verificar se todos os acordos ativos estão quitados
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM cbp_acordos a
            WHERE a.processo_id = ?
            AND a.ativo = 1
            AND (
                EXISTS (
                    SELECT 1 
                    FROM cbp_parcelas_acordo pa 
                    WHERE pa.acordo_id = a.id 
                    AND pa.status != 'PAGO'
                )
                OR EXISTS (
                    SELECT 1 
                    FROM cbp_alvaras_acordo aa 
                    WHERE aa.acordo_id = a.id 
                    AND aa.status = 'PENDENTE'
                )
            )
        ");
        $stmt->execute([$processo['id']]);
        $tem_pendencias = $stmt->fetchColumn() > 0;

        // Se não tem pendências, atualizar status para QUITADO
        if (!$tem_pendencias) {
            $pdo->beginTransaction();
            
            try {
                // Atualizar status do processo
                $stmt = $pdo->prepare("
                    UPDATE cbp_processos_judiciais 
                    SET status_id = ?,
                        updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$status_quitado_id, $processo['id']]);

                // Registrar no histórico
                $stmt = $pdo->prepare("
                    INSERT INTO cbp_historico_status 
                    (processo_id, status_id, data_alteracao, observacoes) 
                    VALUES (?, ?, NOW(), ?)
                ");
                $stmt->execute([
                    $processo['id'],
                    $status_quitado_id,
                    'Status atualizado automaticamente para QUITADO'
                ]);

                $pdo->commit();
            } catch (Exception $e) {
                $pdo->rollBack();
                error_log('Erro ao atualizar status do processo ' . $processo['id'] . ': ' . $e->getMessage());
            }
        }
    }
} catch (Exception $e) {
    error_log('Erro ao verificar status dos processos: ' . $e->getMessage());
} 
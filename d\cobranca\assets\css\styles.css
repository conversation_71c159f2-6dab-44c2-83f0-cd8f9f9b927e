/* Layout Base */
body {
    font-family: 'Roboto', sans-serif;
    background-color: #f8f9fa;
    min-height: 100vh;
}

/* Estilos para badges de status */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-block;
    min-width: 90px;
    text-align: center;
    letter-spacing: 0.3px;
}

.status-badge.status-vigente {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.status-quitado {
    background-color: #cce5ff;
    color: #004085;
}

.status-badge.status-cancelado {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.status-inadimplente {
    background-color: #fff3cd;
    color: #856404;
}

/* Estilos para cards de acordo */
.card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 174, 157, 0.1);
}

.card .progress {
    height: 20px;
    border-radius: 10px;
    background-color: rgba(0, 174, 157, 0.1);
}

.card .progress-bar {
    background-color: #00AE9D;
    border-radius: 10px;
}

.card small.text-muted {
    color: #6c757d;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card p.mb-0 {
    color: #2c3e50;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Form Controls */
.form-control:focus,
.form-select:focus {
    border-color: #00AE9D;
    box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
}

/* View Toggle Buttons */
.btn-group .btn-outline-primary {
    color: #00AE9D;
    border-color: #00AE9D;
}

.btn-group .btn-outline-primary:hover,
.btn-group .btn-outline-primary:focus,
.btn-group .btn-outline-primary.active {
    background-color: #00AE9D;
    border-color: #00AE9D;
    color: #fff;
}

/* Table Styles */
.table {
    margin-bottom: 0;
    font-size: 0.875rem;
}

.table thead th {
    background-color: #003641;
    color: #fff;
    font-weight: 500;
    border-bottom: none;
    padding: 10px 12px;
    white-space: nowrap;
    font-size: 0.8125rem;
}

.table tbody td {
    padding: 8px 12px;
    vertical-align: middle;
    white-space: nowrap;
}

.table tbody tr:hover {
    background-color: rgba(0, 174, 157, 0.05);
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: #00AE9D !important;
    color: #fff !important;
    border-color: #00AE9D !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: rgba(0, 174, 157, 0.1) !important;
    color: #00AE9D !important;
    border-color: #00AE9D !important;
}

.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.875rem;
}

.dataTables_wrapper .dataTables_length select:focus,
.dataTables_wrapper .dataTables_filter input:focus {
    border-color: #00AE9D;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
}

.dataTables_wrapper .dataTables_info {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Progress Bar in Table */
.table .progress {
    margin-bottom: 0;
    background-color: rgba(0, 174, 157, 0.1);
    height: 15px;
    width: 100px;
}

.table .progress-bar {
    font-size: 0.75rem;
    line-height: 15px;
    font-weight: 600;
}
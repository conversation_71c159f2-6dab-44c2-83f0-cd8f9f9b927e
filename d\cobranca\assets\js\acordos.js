$(document).ready(function() {
    // Verificar estrutura da tabela antes da inicialização
    const table = $('#tabelaAcordos');
    const headerColumns = table.find('thead tr th').length;
    const bodyRows = table.find('tbody tr');
    
    console.log('Número de colunas no cabeçalho:', headerColumns);
    bodyRows.each(function(index) {
        const rowColumns = $(this).find('td').length;
        console.log(`Linha ${index + 1}: ${rowColumns} colunas`);
    });

    // Inicializar DataTables com configuração mínima
    const tabelaAcordos = $('#tabelaAcordos').DataTable({
        retrieve: true,
        deferRender: true,
        processing: true,
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/pt-BR.json'
        },
        pageLength: 10,
        order: [[0, 'desc']],
        columnDefs: [
            {
                targets: 1,
                className: 'cliente-cell'
            }
        ]
    });

    // Evento após a inicialização
    tabelaAcordos.on('init', function() {
        console.log('DataTables inicializado');
        console.log('Número de colunas após inicialização:', tabelaAcordos.columns().count());
    });
    
    // Ajustar colunas quando a janela for redimensionada
    $(window).on('resize', function() {
        tabelaAcordos.columns.adjust().draw();
    });
    
    // Ajustar colunas quando alternar entre as visualizações
    $('#viewList').on('click', function() {
        setTimeout(function() {
            tabelaAcordos.columns.adjust().draw();
        }, 10);
    });
    
    // Ajustar colunas após a inicialização
    setTimeout(function() {
        tabelaAcordos.columns.adjust().draw();
    }, 100);

    // Função para atualizar a tabela de acordos
    window.atualizarTabelaAcordos = function() {
        // Atualizar a página inteira para garantir que todos os componentes sejam atualizados
        window.location.reload();
    };

    // Função para atualizar estatísticas
    function atualizarEstatisticas() {
        $.ajax({
            url: 'ajax/estatisticas_acordos.php',
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    // Atualizar os contadores na página
                    $('#totalAcordos').text(response.total);
                    $('#acordosVigentes').text(response.vigentes);
                    $('#acordosQuitados').text(response.quitados);
                    $('#acordosInadimplentes').text(response.inadimplentes);
                }
            }
        });
    }

    // Função para registrar pagamento
    window.registrarPagamento = function(acordoId) {
        $.ajax({
            url: 'ajax/form_pagamento.php',
            type: 'GET',
            data: { acordo_id: acordoId },
            success: function(response) {
                $('#modalRegistrarPagamento .modal-body').html(response);
                $('#modalRegistrarPagamento').modal('show');

                // Inicializar máscaras e validações do formulário
                inicializarFormularioPagamento();
            }
        });
    };

    // Função para salvar pagamento
    window.salvarPagamento = function(formElement) {
        const form = $(formElement);
        const formData = new FormData(formElement);

        $.ajax({
            url: 'ajax/salvar_pagamento.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Fechar modal de pagamento
                    $('#modalRegistrarPagamento').modal('hide');
                    
                    // Atualizar modal de detalhes do acordo
                    if ($('#modalVisualizarAcordo').is(':visible')) {
                        atualizarDetalhesAcordo($('#modalVisualizarAcordo').data('acordo-id'));
                    }
                    
                    // Atualizar a tabela principal
                    atualizarTabelaAcordos();
                    
                    // Mostrar mensagem de sucesso
                    Swal.fire({
                        icon: 'success',
                        title: 'Sucesso!',
                        text: 'Pagamento registrado com sucesso!'
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro!',
                        text: response.message || 'Erro ao registrar pagamento.'
                    });
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Erro!',
                    text: 'Erro ao processar a requisição.'
                });
            }
        });

        return false; // Prevenir submit padrão do formulário
    };

    // Função para atualizar detalhes do acordo no modal
    function atualizarDetalhesAcordo(acordoId) {
        $.ajax({
            url: 'ajax/visualizar_acordo.php',
            type: 'GET',
            data: { acordo_id: acordoId },
            success: function(response) {
                $('#modalVisualizarAcordo .modal-body').html(response);
            }
        });
    }

    // Inicializar máscaras e validações do formulário de pagamento
    function inicializarFormularioPagamento() {
        $('.money').inputmask('currency', {
            radixPoint: ',',
            groupSeparator: '.',
            allowMinus: false,
            prefix: 'R$ ',
            digits: 2,
            digitsOptional: false,
            rightAlign: false,
            unmaskAsNumber: true
        });

        $('.date').inputmask('99/99/9999');
    }
}); 
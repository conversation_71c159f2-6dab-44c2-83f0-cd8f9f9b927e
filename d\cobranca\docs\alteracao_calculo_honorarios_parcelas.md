# Alteração no Cálculo de Honorários para Parcelas

## Data da Alteração
**Data:** [Data atual]  
**Solicitante:** [Nome do solicitante]  
**Desenvolvedor:** Augment Agent  

## Resumo da Alteração

Foi implementada uma nova regra para o cálculo de honorários de **PARCELAS** no sistema de cobrança judicial. A alteração garante que os honorários sejam sempre calculados sobre o menor valor entre o valor pago e o valor original da parcela.

## Regra Anterior

**Antes:** Os honorários eram calculados sempre sobre o valor efetivamente pago (`valor_pago`), independentemente do valor original da parcela.

### Exemplos da Regra Anterior:
- Valor da parcela: R$ 1.000,00 | Valor pago: R$ 800,00 → Honorário sobre R$ 800,00
- Valor da parcela: R$ 1.000,00 | Valor pago: R$ 1.200,00 → Honorário sobre R$ 1.200,00

## Nova Regra

**Agora:** Os honorários são calculados sempre sobre o **MENOR** valor entre `valor_pago` e `valor` da parcela.

### Fórmula:
```
valor_para_calculo = MIN(valor_pago, valor_parcela)
valor_honorario = ROUND(valor_para_calculo * porcentagem / 100, 2)
```

### Exemplos da Nova Regra:
- Valor da parcela: R$ 1.000,00 | Valor pago: R$ 800,00 → Honorário sobre R$ 800,00 ✅
- Valor da parcela: R$ 1.000,00 | Valor pago: R$ 1.200,00 → Honorário sobre R$ 1.000,00 ✅
- Valor da parcela: R$ 1.000,00 | Valor pago: R$ 1.000,00 → Honorário sobre R$ 1.000,00 ✅

## Arquivos Alterados

### 1. `includes/hooks/honorarios_hooks.php`
**Função:** `onParcelaPaga()`  
**Alteração:** Implementada lógica `min($parcela['valor_pago'], $parcela['valor'])` para determinar o valor base do cálculo.

### 2. `debug_honorarios.php`
**Alteração:** Ajustada a lógica de cálculo para usar o menor valor e atualizado o valor passado para o método `registrarHonorario()`.

### 3. `sql/honorarios_fix_v2.sql`
**Alteração:** Script SQL atualizado para usar `LEAST(pa.valor_pago, pa.valor)` em vez de apenas `pa.valor_pago`.

### 4. `teste_porcentagem_zero.php`
**Alteração:** Adicionados testes para validar a nova lógica do menor valor.

## Impacto

### ✅ Benefícios:
- **Controle de custos:** Honorários não excedem o valor original da parcela
- **Transparência:** Cálculo mais justo quando há pagamentos acima do valor devido
- **Consistência:** Regra clara e previsível para todos os casos

### ⚠️ Considerações:
- **Apenas PARCELAS:** Esta regra se aplica somente a parcelas, não afeta entradas nem alvarás
- **Retroativo:** Novos honorários seguirão a nova regra; honorários já calculados permanecem inalterados
- **Logs:** Sistema registra nos logs qual valor foi usado para o cálculo

## Casos de Uso

### Caso 1: Pagamento Parcial
- **Situação:** Cliente paga menos que o valor da parcela
- **Comportamento:** Honorário calculado sobre o valor pago (menor)
- **Exemplo:** Parcela R$ 1.000 | Pago R$ 800 → Honorário sobre R$ 800

### Caso 2: Pagamento com Acréscimo
- **Situação:** Cliente paga mais que o valor da parcela (juros, multa, etc.)
- **Comportamento:** Honorário calculado sobre o valor da parcela (menor)
- **Exemplo:** Parcela R$ 1.000 | Pago R$ 1.200 → Honorário sobre R$ 1.000

### Caso 3: Pagamento Exato
- **Situação:** Cliente paga exatamente o valor da parcela
- **Comportamento:** Honorário calculado sobre o valor (ambos são iguais)
- **Exemplo:** Parcela R$ 1.000 | Pago R$ 1.000 → Honorário sobre R$ 1.000

## Logs e Monitoramento

O sistema registra nos logs as seguintes informações:
```
Aplicando regra do menor valor - Valor da parcela: 1000.00, Valor pago: 1200.00, Valor usado para honorário: 1000.00
```

## Testes

Execute o arquivo `teste_porcentagem_zero.php` para validar:
1. Cenários de pagamento menor, maior e igual ao valor da parcela
2. Cálculos corretos de honorários
3. Aplicação correta da função `min()`

## Observações Importantes

- **Entradas e Alvarás:** Continuam usando o valor integral (não aplicam a regra do menor valor)
- **Porcentagem 0%:** Continua funcionando normalmente com a nova regra
- **Compatibilidade:** Alteração não quebra funcionalidades existentes
- **Performance:** Impacto mínimo na performance do sistema

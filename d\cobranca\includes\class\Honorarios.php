<?php

class Honorarios {
    private $pdo;
    private $porcentagem_padrao;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->porcentagem_padrao = $this->getPorcentagemPadrao();
        error_log("Honorarios class initialized with default percentage: " . $this->porcentagem_padrao);
    }

    /**
     * Obtém a porcentagem padrão de honorários
     */
    private function getPorcentagemPadrao() {
        $stmt = $this->pdo->prepare("SELECT valor FROM cbp_configuracoes WHERE chave = 'porcentagem_padrao_honorarios'");
        $stmt->execute();
        $porcentagem = $stmt->fetchColumn();
        error_log("Porcentagem padrão obtida do banco: " . ($porcentagem ?: "não encontrada, usando padrão"));
        return $porcentagem ?: 13.0435; // Valor padrão caso não esteja configurado
    }

    /**
     * Registra honorário automaticamente
     */
    public function registrarHonorario($dados) {
        try {
            error_log("=== INÍCIO DO REGISTRO DE HONORÁRIO ===");
            error_log("Dados recebidos: " . print_r($dados, true));
            
            // Validar dados obrigatórios
            $campos_obrigatorios = ['tipo', 'valor_recebido', 'processo_id', 'data_recebimento'];
            foreach ($campos_obrigatorios as $campo) {
                if (empty($dados[$campo])) {
                    throw new Exception("Campo obrigatório não fornecido: $campo");
                }
            }
            
            // Preparar dados básicos
            $tipo = $dados['tipo'];
            $valor_recebido = floatval(str_replace(',', '.', $dados['valor_recebido']));
            $processo_id = intval($dados['processo_id']);
            $data_recebimento = $dados['data_recebimento'];
            
            error_log("Dados validados e convertidos:");
            error_log("- Tipo: $tipo");
            error_log("- Valor recebido: $valor_recebido");
            error_log("- Processo ID: $processo_id");
            error_log("- Data recebimento: $data_recebimento");
            
            // Buscar informações do processo e acordo
            $stmt = $this->pdo->prepare("
                SELECT 
                    p.id,
                    p.advogado_id,
                    p.associado_nome,
                    p.associado_documento,
                    COALESCE(
                        (SELECT porcentagem_honorario 
                         FROM cbp_acordos 
                         WHERE processo_id = p.id 
                         AND id = (
                             SELECT acordo_id 
                             FROM cbp_parcelas_acordo 
                             WHERE id = :parcela_id
                         )
                        ),
                        (SELECT porcentagem_honorario 
                         FROM cbp_acordos 
                         WHERE processo_id = p.id 
                         ORDER BY created_at DESC 
                         LIMIT 1)
                    ) as porcentagem_personalizada
                FROM cbp_processos_judiciais p
                WHERE p.id = :processo_id
            ");
            
            $stmt->execute([
                'processo_id' => $processo_id,
                'parcela_id' => $dados['parcela_id'] ?? null
            ]);
            $processo = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$processo) {
                throw new Exception("Processo não encontrado: $processo_id");
            }
            
            error_log("Dados do processo obtidos:");
            error_log(print_r($processo, true));

            // Buscar ou criar associado
            try {
                $stmt = $this->pdo->prepare("
                    SELECT id 
                    FROM cbp_associados 
                    WHERE cpf_cnpj = ? 
                    LIMIT 1
                ");
                $stmt->execute([$processo['associado_documento']]);
                $associado = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Se não encontrou o associado, vamos criá-lo
                if (!$associado) {
                    error_log("Associado não encontrado, criando novo com documento: " . $processo['associado_documento']);
                    $stmt = $this->pdo->prepare("
                        INSERT INTO cbp_associados (
                            nome, 
                            cpf_cnpj, 
                            created_at, 
                            updated_at
                        ) VALUES (
                            ?, 
                            ?,
                            NOW(), 
                            NOW()
                        )
                    ");
                    $stmt->execute([
                        $processo['associado_nome'],
                        $processo['associado_documento']
                    ]);
                    $associado_id = $this->pdo->lastInsertId();
                    error_log("Novo associado criado com ID: $associado_id");
                } else {
                    $associado_id = $associado['id'];
                    error_log("Associado encontrado com ID: $associado_id");
                }
            } catch (Exception $e) {
                error_log("Erro ao buscar/criar associado: " . $e->getMessage());
                $associado_id = null; // Continuar mesmo sem associado
            }

            // Determinar porcentagem a ser usada
            $porcentagem = $processo['porcentagem_personalizada'] ?: $this->porcentagem_padrao;
            error_log("Porcentagem final a ser usada: $porcentagem%");
            
            // Calcular valor do honorário
            $valor_honorario = round($valor_recebido * $porcentagem / 100, 2);
            error_log("Valor do honorário calculado: $valor_honorario");

            // Verificar se já existe um honorário registrado para esta parcela/entrada/alvará
            error_log("Verificando duplicidade de honorário - Processo: $processo_id, Tipo: $tipo, Data: $data_recebimento, Valor: $valor_recebido");
            
            $stmt = $this->pdo->prepare("
                SELECT id 
                FROM cbp_honorarios 
                WHERE processo_id = :processo_id 
                AND tipo = :tipo 
                AND data_recebimento = :data_recebimento
                AND valor_recebido = :valor_recebido
                AND numero_parcela = :numero_parcela
            ");
            
            $stmt->execute([
                'processo_id' => $processo_id,
                'tipo' => $tipo,
                'data_recebimento' => $data_recebimento,
                'valor_recebido' => $valor_recebido,
                'numero_parcela' => $dados['numero_parcela'] ?? null
            ]);
            
            $honorario_existente = $stmt->fetchColumn();
            if ($honorario_existente) {
                error_log("AVISO: Honorário já registrado para este pagamento (ID: $honorario_existente). Evitando duplicação.");
                return true;
            } else {
                error_log("Nenhum honorário duplicado encontrado. Prosseguindo com o registro.");
            }

            // Inserir registro de honorário
            $stmt = $this->pdo->prepare("
                INSERT INTO cbp_honorarios (
                    processo_id,
                    associado_id,
                    advogado_id,
                    tipo,
                    valor_recebido,
                    porcentagem_honorario,
                    valor_honorario,
                    status,
                    data_recebimento,
                    numero_parcela,
                    total_parcelas,
                    created_at,
                    updated_at
                ) VALUES (
                    :processo_id,
                    :associado_id,
                    :advogado_id,
                    :tipo,
                    :valor_recebido,
                    :porcentagem_honorario,
                    :valor_honorario,
                    'PENDENTE',
                    :data_recebimento,
                    :numero_parcela,
                    :total_parcelas,
                    NOW(),
                    NOW()
                )
            ");

            $params = [
                'processo_id' => $processo_id,
                'associado_id' => $associado_id,
                'advogado_id' => $processo['advogado_id'],
                'tipo' => $tipo,
                'valor_recebido' => $valor_recebido,
                'porcentagem_honorario' => $porcentagem,
                'valor_honorario' => $valor_honorario,
                'data_recebimento' => $data_recebimento,
                'numero_parcela' => $dados['numero_parcela'] ?? null,
                'total_parcelas' => $dados['total_parcelas'] ?? null
            ];

            error_log("Tentando inserir honorário com parâmetros:");
            error_log(print_r($params, true));
            
            $stmt->execute($params);
            $honorario_id = $this->pdo->lastInsertId();
            error_log("Honorário registrado com sucesso! ID: $honorario_id");
            error_log("=== FIM DO REGISTRO DE HONORÁRIO ===");
            return true;

        } catch (Exception $e) {
            error_log("=== ERRO AO REGISTRAR HONORÁRIO ===");
            error_log("Mensagem: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            error_log("=== FIM DO ERRO ===");
            throw $e; // Relançar a exceção para ser tratada no nível superior
        }
    }

    /**
     * Atualiza o status do honorário
     */
    public function atualizarStatus($honorario_id, $novo_status) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE cbp_honorarios 
                SET status = :status, updated_at = NOW() 
                WHERE id = :id
            ");
            return $stmt->execute([
                'status' => $novo_status,
                'id' => $honorario_id
            ]);
        } catch (Exception $e) {
            error_log("Erro ao atualizar status do honorário: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Lista todos os honorários com filtros
     */
    public function listarHonorarios($filtros = []) {
        try {
            $where = ["1=1"];
            $params = [];

            if (!empty($filtros['status']) && $filtros['status'] !== 'todos') {
                $where[] = "h.status = :status";
                $params['status'] = $filtros['status'];
            }

            if (!empty($filtros['tipo']) && $filtros['tipo'] !== 'todos') {
                $where[] = "h.tipo = :tipo";
                $params['tipo'] = $filtros['tipo'];
            }

            // Modificação para aceitar múltiplos IDs de associados
            if (!empty($filtros['associado_ids'])) {
                $placeholders = [];
                foreach ($filtros['associado_ids'] as $key => $id) {
                    $paramName = "associado_id_" . $key;
                    $placeholders[] = ":$paramName";
                    $params[$paramName] = $id;
                }
                $where[] = "h.associado_id IN (" . implode(',', $placeholders) . ")";
            } else if (!empty($filtros['associado_id'])) {
                $where[] = "h.associado_id = :associado_id";
                $params['associado_id'] = $filtros['associado_id'];
            }

            if (!empty($filtros['advogado_id'])) {
                $where[] = "h.advogado_id = :advogado_id";
                $params['advogado_id'] = $filtros['advogado_id'];
            }

            if (!empty($filtros['pa_id'])) {
                $where[] = "p.pa_id = :pa_id";
                $params['pa_id'] = $filtros['pa_id'];
            }

            if (!empty($filtros['data_pagamento_inicio'])) {
                $where[] = "h.data_pagamento >= :data_pagamento_inicio";
                $params['data_pagamento_inicio'] = $filtros['data_pagamento_inicio'];
            }

            if (!empty($filtros['data_pagamento_fim'])) {
                $where[] = "h.data_pagamento <= :data_pagamento_fim";
                $params['data_pagamento_fim'] = $filtros['data_pagamento_fim'];
            }

            $sql = "
                SELECT 
                    h.*,
                    a.nome AS nome_associado,
                    adv.nome AS nome_advogado,
                    p.numero_processo,
                    pa.numero,
                    pa.nome AS pa_nome,
                    CONCAT('PA ', pa.numero, ' - ', pa.nome) as pa_completo,
                    DATE_FORMAT(h.data_recebimento, '%d/%m/%Y') as data_recebimento_formatada,
                    DATE_FORMAT(h.data_pagamento, '%d/%m/%Y') as data_pagamento_formatada,
                    FORMAT(h.valor_recebido, 2, 'pt_BR') as valor_recebido_formatado,
                    CONCAT(FORMAT(h.porcentagem_honorario, 4, 'pt_BR'), '%') as porcentagem_formatada,
                    FORMAT(h.valor_honorario, 2, 'pt_BR') as valor_honorario_formatado,
                    CASE 
                        WHEN h.tipo = 'PARCELA' THEN 
                            CONCAT('PARCELA ', h.numero_parcela, '/', h.total_parcelas)
                        ELSE h.tipo
                    END as descricao_tipo
                FROM cbp_honorarios h
                LEFT JOIN cbp_associados a ON h.associado_id = a.id
                LEFT JOIN cbp_advogados adv ON h.advogado_id = adv.id
                LEFT JOIN cbp_processos_judiciais p ON h.processo_id = p.id
                LEFT JOIN pontos_atendimento pa ON p.pa_id = pa.id
                WHERE " . implode(" AND ", $where) . "
                ORDER BY h.data_recebimento DESC, h.created_at DESC
            ";

            error_log("SQL da listagem de honorários: " . $sql);
            error_log("Parâmetros: " . print_r($params, true));

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);

            error_log("Total de registros encontrados: " . count($resultados));

            // Formatar valores monetários e datas
            foreach ($resultados as &$row) {
                $row['valor_recebido_formatado'] = 'R$ ' . number_format($row['valor_recebido'], 2, ',', '.');
                $row['valor_honorario_formatado'] = 'R$ ' . number_format($row['valor_honorario'], 2, ',', '.');
                $row['porcentagem_formatada'] = number_format($row['porcentagem_honorario'], 4, ',', '.') . '%';
                $row['data_recebimento_formatada'] = date('d/m/Y', strtotime($row['data_recebimento']));
                $row['created_at_formatado'] = date('d/m/Y H:i:s', strtotime($row['created_at']));
            }

            return $resultados;

        } catch (Exception $e) {
            error_log("Erro ao listar honorários: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Obtém estatísticas dos honorários
     */
    public function getEstatisticas($filtros = []) {
        try {
            $where = ["1=1"];
            $params = [];

            // Aplicar os mesmos filtros das listagens
            if (!empty($filtros['status'])) {
                $where[] = "status = :status";
                $params['status'] = $filtros['status'];
            }
            // ... (aplicar outros filtros conforme necessário)

            $sql = "
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'PENDENTE' THEN 1 ELSE 0 END) as pendentes,
                    SUM(CASE WHEN status = 'PAGO' THEN 1 ELSE 0 END) as pagos,
                    SUM(valor_honorario) as valor_total,
                    SUM(CASE WHEN status = 'PENDENTE' THEN valor_honorario ELSE 0 END) as valor_pendente,
                    SUM(CASE WHEN status = 'PAGO' THEN valor_honorario ELSE 0 END) as valor_pago
                FROM cbp_honorarios
                WHERE " . implode(" AND ", $where);

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Erro ao obter estatísticas: " . $e->getMessage());
            return [
                'total' => 0,
                'pendentes' => 0,
                'pagos' => 0,
                'valor_total' => 0,
                'valor_pendente' => 0,
                'valor_pago' => 0
            ];
        }
    }

    /**
     * Obtém estatísticas específicas para os cards da página de honorários
     * Este método é independente dos filtros e sempre retorna os totais gerais
     */
    public function getEstatisticasCards() {
        try {
            $sql = "
                SELECT 
                    COUNT(*) as total,
                    COALESCE(SUM(CASE WHEN status = 'PENDENTE' THEN 1 ELSE 0 END), 0) as pendentes,
                    COALESCE(SUM(CASE WHEN status = 'PAGO' THEN 1 ELSE 0 END), 0) as pagos,
                    COALESCE(SUM(valor_honorario), 0) as valor_total,
                    COALESCE(SUM(CASE WHEN status = 'PENDENTE' THEN valor_honorario ELSE 0 END), 0) as valor_pendente,
                    COALESCE(SUM(CASE WHEN status = 'PAGO' THEN valor_honorario ELSE 0 END), 0) as valor_pago
                FROM cbp_honorarios
                WHERE status IN ('PENDENTE', 'PAGO')";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $resultado = $stmt->fetch(PDO::FETCH_ASSOC);

            // Garantir que todos os campos existam, mesmo que nulos
            return array_merge([
                'total' => 0,
                'pendentes' => 0,
                'pagos' => 0,
                'valor_total' => 0,
                'valor_pendente' => 0,
                'valor_pago' => 0
            ], $resultado ?: []);

        } catch (Exception $e) {
            error_log("Erro ao obter estatísticas dos cards: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            
            // Retornar valores zerados em caso de erro
            return [
                'total' => 0,
                'pendentes' => 0,
                'pagos' => 0,
                'valor_total' => 0,
                'valor_pendente' => 0,
                'valor_pago' => 0
            ];
        }
    }
} 
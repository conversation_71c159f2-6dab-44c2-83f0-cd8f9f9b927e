<?php
require_once __DIR__ . '/../class/Honorarios.php';

/**
 * Hooks para registrar honorários automaticamente
 */
class HonorariosHooks {
    private $honorarios;
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->honorarios = new Honorarios($pdo);
    }

    /**
     * Registra honorário quando uma parcela é paga
     */
    public function onParcelaPaga($parcela_id) {
        error_log("=== INÍCIO DO HOOK onParcelaPaga ===");
        error_log("Parcela ID recebido: " . $parcela_id);
        
        try {
            // Buscar informações da parcela com todos os dados necessários
            $stmt = $this->pdo->prepare("
                SELECT 
                    p.*,
                    a.processo_id,
                    a.quantidade_parcelas,
                    a.porcentagem_honorario
                FROM cbp_parcelas_acordo p
                INNER JOIN cbp_acordos a ON p.acordo_id = a.id
                WHERE p.id = ?
            ");
            $stmt->execute([$parcela_id]);
            $parcela = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$parcela) {
                throw new Exception("Parcela não encontrada: " . $parcela_id);
            }

            error_log("Dados da parcela obtidos:");
            error_log(print_r($parcela, true));
            
            if ($parcela['status'] !== 'PAGO') {
                throw new Exception("Parcela não está com status PAGO");
            }

            // Aplicar nova regra: usar sempre o menor valor entre valor_pago e valor da parcela
            if (empty($parcela['valor_pago'])) {
                error_log("AVISO: valor_pago está vazio, usando valor original da parcela");
                $valor_recebido = $parcela['valor_parcela'];
            } else {
                // Usar o menor valor entre valor_pago e valor_parcela
                $valor_recebido = min($parcela['valor_pago'], $parcela['valor_parcela']);
                error_log("Aplicando regra do menor valor - Valor da parcela: " . $parcela['valor_parcela'] .
                         ", Valor pago: " . $parcela['valor_pago'] .
                         ", Valor usado para honorário (menor): " . $valor_recebido);
            }
            
            // Registrar o honorário
            $resultado = $this->honorarios->registrarHonorario([
                'tipo' => 'PARCELA',
                'valor_recebido' => $valor_recebido,
                'processo_id' => $parcela['processo_id'],
                'data_recebimento' => $parcela['data_pagamento'],
                'numero_parcela' => $parcela['numero_parcela'],
                'total_parcelas' => $parcela['quantidade_parcelas'],
                'parcela_id' => $parcela['id']
            ]);
            
            error_log("Honorário registrado com sucesso");
            error_log("=== FIM DO HOOK onParcelaPaga - SUCESSO ===");
            
            return $resultado;
        } catch (Exception $e) {
            error_log("=== ERRO NO HOOK onParcelaPaga ===");
            error_log("Mensagem: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            error_log("=== FIM DO ERRO ===");
            throw $e;
        }
    }

    /**
     * Registra honorário quando uma entrada é paga
     */
    public function onEntradaPaga($acordo_id) {
        error_log("=== INÍCIO DO HOOK onEntradaPaga ===");
        error_log("Acordo ID recebido: " . $acordo_id);
        
        try {
            // Buscar informações da entrada
            $stmt = $this->pdo->prepare("
                SELECT 
                    a.processo_id,
                    a.valor_entrada,
                    a.data_entrada,
                    a.porcentagem_honorario
                FROM cbp_acordos a
                WHERE a.id = ? AND a.valor_entrada > 0
            ");
            $stmt->execute([$acordo_id]);
            $acordo = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$acordo) {
                throw new Exception("Acordo não encontrado ou não possui entrada: " . $acordo_id);
            }

            error_log("Dados do acordo obtidos:");
            error_log(print_r($acordo, true));

            // Registrar o honorário
            $resultado = $this->honorarios->registrarHonorario([
                'tipo' => 'ENTRADA',
                'valor_recebido' => $acordo['valor_entrada'],
                'processo_id' => $acordo['processo_id'],
                'data_recebimento' => $acordo['data_entrada']
            ]);
            
            error_log("Honorário da entrada registrado com sucesso");
            error_log("=== FIM DO HOOK onEntradaPaga - SUCESSO ===");
            
            return $resultado;
        } catch (Exception $e) {
            error_log("=== ERRO NO HOOK onEntradaPaga ===");
            error_log("Mensagem: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            error_log("=== FIM DO ERRO ===");
            throw $e;
        }
    }

    /**
     * Registra honorário quando um alvará é recebido
     */
    public function onAlvaraRecebido($alvara_id) {
        error_log("=== INÍCIO DO HOOK onAlvaraRecebido ===");
        error_log("Alvará ID recebido: " . $alvara_id);
        
        try {
            // Buscar informações do alvará
            $stmt = $this->pdo->prepare("
                SELECT *
                FROM cbp_alvaras
                WHERE id = ? AND situacao = 'RECEBIDO'
            ");
            $stmt->execute([$alvara_id]);
            $alvara = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$alvara) {
                throw new Exception("Alvará não encontrado ou não está recebido: " . $alvara_id);
            }

            error_log("Dados do alvará obtidos:");
            error_log(print_r($alvara, true));

            // Registrar o honorário
            $resultado = $this->honorarios->registrarHonorario([
                'tipo' => 'ALVARA',
                'valor_recebido' => $alvara['valor'],
                'processo_id' => $alvara['processo_id'],
                'data_recebimento' => $alvara['data_recebimento']
            ]);
            
            error_log("Honorário do alvará registrado com sucesso");
            error_log("=== FIM DO HOOK onAlvaraRecebido - SUCESSO ===");
            
            return $resultado;
        } catch (Exception $e) {
            error_log("=== ERRO NO HOOK onAlvaraRecebido ===");
            error_log("Mensagem: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            error_log("=== FIM DO ERRO ===");
            throw $e;
        }
    }
} 
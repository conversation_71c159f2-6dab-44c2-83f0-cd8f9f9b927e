<?php
require_once __DIR__ . '/../hooks/honorarios_hooks.php';

/**
 * Registra os triggers necessários para os honorários
 */
function registrarTriggersHonorarios() {
    global $pdo;
    $hooks = new HonorariosHooks($pdo);

    // Registrar trigger para parcelas pagas
    if (isset($_POST['status']) && $_POST['status'] === 'PAGO' && isset($_POST['parcela_id'])) {
        $hooks->onParcelaPaga($_POST['parcela_id']);
    }

    // Registrar trigger para entradas pagas
    if (isset($_POST['acordo_id']) && isset($_POST['valor_entrada']) && $_POST['valor_entrada'] > 0) {
        $hooks->onEntradaPaga($_POST['acordo_id']);
    }

    // Registrar trigger para alvarás recebidos
    if (isset($_POST['situacao']) && $_POST['situacao'] === 'RECEBIDO' && isset($_POST['alvara_id'])) {
        $hooks->onAlvaraRecebido($_POST['alvara_id']);
    }
}

// Registrar os triggers
registrarTriggersHonorarios(); 
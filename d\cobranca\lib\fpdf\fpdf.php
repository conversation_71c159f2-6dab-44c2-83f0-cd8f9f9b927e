<?php
// Classe FPDF simplificada para teste
class FPDF {
    protected $page = 1;
    protected $font = 'Arial';
    protected $fontStyle = '';
    protected $fontSize = 12;
    
    public function AddPage() {
        // Simulando adição de uma página
        return true;
    }
    
    public function SetFont($font = 'Arial', $style = '', $size = 12) {
        $this->font = $font;
        $this->fontStyle = $style;
        $this->fontSize = $size;
        return true;
    }
    
    public function Cell($w, $h = 0, $txt = '', $border = 0, $ln = 0, $align = '', $fill = false, $link = '') {
        // Simulando criação de uma célula
        return true;
    }
    
    public function Ln($h = null) {
        // Simulando quebra de linha
        return true;
    }
    
    public function PageNo() {
        return $this->page;
    }
    
    public function Output($dest = '', $name = '', $isUTF8 = false) {
        // Simulando saída do PDF
        return true;
    }
} 
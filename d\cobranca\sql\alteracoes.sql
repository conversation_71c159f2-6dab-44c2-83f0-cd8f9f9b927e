-- <PERSON><PERSON><PERSON><PERSON> coluna pa_id à tabela cbp_contratos
ALTER TABLE cbp_contratos ADD COLUMN pa_id INT NULL;

-- Adici<PERSON>r chave estrangeira
ALTER TABLE cbp_contratos 
ADD CONSTRAINT fk_contrato_pa 
FOREIGN KEY (pa_id) REFERENCES pontos_atendimento(id);

-- <PERSON><PERSON>r índice para melhor performance
CREATE INDEX idx_contrato_pa ON cbp_contratos(pa_id);

-- Atualizar contratos existentes para usar o PA do processo (se houver)
UPDATE cbp_contratos c
INNER JOIN cbp_processos_contratos pc ON c.id = pc.contrato_id
INNER JOIN cbp_processos_judiciais p ON pc.processo_id = p.id
SET c.pa_id = p.pa_id
WHERE c.pa_id IS NULL;

-- <PERSON><PERSON><PERSON> tabel<PERSON> de modalidades se não existir
CREATE TABLE IF NOT EXISTS cbp_modalidades_processo (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Criar índice para melhor performance nas buscas por nome
CREATE INDEX IF NOT EXISTS idx_modalidade_nome ON cbp_modalidades_processo(nome); 
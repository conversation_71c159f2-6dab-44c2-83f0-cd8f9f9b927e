-- Script para criar/sincronizar registros de honorários a partir de outras tabelas
-- Este script gera registros na tabela cbp_honorarios a partir de dados em outras tabelas

-- 1. Verificar se a tabela de configuração existe e tem a porcentagem padrão
SET @porcentagem_padrao = (SELECT valor FROM cbp_configuracoes WHERE chave = 'porcentagem_padrao_honorarios');
SET @porcentagem_padrao = IFNULL(@porcentagem_padrao, 13.0435);

-- Mostrar porcentagem configurada
SELECT CONCAT('Porcentagem padrão de honorários: ', @porcentagem_padrao, '%') as mensagem;

-- 2. Sincronizar honorários de parcelas de acordo
SELECT 'Sincronizando honorários de parcelas pagas...' as mensagem;

-- Inserir honorários para parcelas pagas que ainda não têm correspondência
INSERT INTO cbp_honorarios (
    processo_id,
    associado_id,
    advogado_id,
    tipo,
    valor_recebido,
    porcentagem_honorario,
    valor_honorario,
    status,
    data_recebimento,
    numero_parcela,
    total_parcelas,
    created_at,
    updated_at
)
SELECT 
    ac.processo_id,
    pj.associado_id,
    pj.advogado_id,
    'PARCELA',
    p.valor,
    @porcentagem_padrao,
    ROUND(p.valor * @porcentagem_padrao / 100, 2),
    'PENDENTE',
    p.data_pagamento,
    p.numero_parcela,
    ac.quantidade_parcelas,
    NOW(),
    NOW()
FROM 
    cbp_parcelas_acordo p
    INNER JOIN cbp_acordos ac ON p.acordo_id = ac.id
    INNER JOIN cbp_processos_judiciais pj ON ac.processo_id = pj.id
WHERE 
    p.status = 'PAGO'
    AND NOT EXISTS (
        SELECT 1 
        FROM cbp_honorarios h 
        WHERE h.processo_id = ac.processo_id 
        AND h.tipo = 'PARCELA' 
        AND h.numero_parcela = p.numero_parcela
    );

-- Contabilizar quantos registros foram inseridos
SELECT CONCAT('Inseridos ', ROW_COUNT(), ' honorários de parcelas') as resultado;

-- 3. Sincronizar honorários de alvarás
SELECT 'Sincronizando honorários de alvarás...' as mensagem;

-- Inserir honorários para alvarás que ainda não têm correspondência
INSERT INTO cbp_honorarios (
    processo_id,
    associado_id,
    advogado_id,
    tipo,
    valor_recebido,
    porcentagem_honorario,
    valor_honorario,
    status,
    data_recebimento,
    created_at,
    updated_at
)
SELECT 
    a.processo_id,
    pj.associado_id,
    pj.advogado_id,
    'ALVARA',
    a.valor,
    @porcentagem_padrao,
    ROUND(a.valor * @porcentagem_padrao / 100, 2),
    'PENDENTE',
    a.data_recebimento,
    NOW(),
    NOW()
FROM 
    cbp_alvaras a
    INNER JOIN cbp_processos_judiciais pj ON a.processo_id = pj.id
WHERE 
    a.situacao = 'RECEBIDO'
    AND NOT EXISTS (
        SELECT 1 
        FROM cbp_honorarios h 
        WHERE h.processo_id = a.processo_id 
        AND h.tipo = 'ALVARA' 
        AND DATE(h.data_recebimento) = DATE(a.data_recebimento)
        AND h.valor_recebido = a.valor
    );

-- Contabilizar quantos registros foram inseridos
SELECT CONCAT('Inseridos ', ROW_COUNT(), ' honorários de alvarás') as resultado;

-- 4. Verificar totais atualizados
SELECT 'Resumo de honorários após sincronização:' as mensagem;
SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN tipo = 'PARCELA' THEN 1 ELSE 0 END) as parcelas,
    SUM(CASE WHEN tipo = 'ALVARA' THEN 1 ELSE 0 END) as alvaras,
    SUM(CASE WHEN status = 'PENDENTE' THEN 1 ELSE 0 END) as pendentes,
    SUM(CASE WHEN status = 'PAGO' THEN 1 ELSE 0 END) as pagos,
    SUM(valor_honorario) as valor_total
FROM cbp_honorarios; 
-- Script para corrigir a geração automática de honorários
-- Criado em (data)

-- 1. Verificar se já existe a tabela de status de honorários
CREATE TABLE IF NOT EXISTS `cbp_status_honorarios` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2. Inserir os status básicos (se não existirem)
INSERT IGNORE INTO `cbp_status_honorarios` (`id`, `nome`) VALUES 
(1, 'PENDENTE'),
(2, 'PAGO');

-- 3. Adicionar campos de controle nas tabelas de alvarás e parcelas se não existirem
ALTER TABLE `cbp_alvaras` 
ADD COLUMN IF NOT EXISTS `honorario_id` int(11) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `honorario_status` enum('PENDENTE','PAGO') DEFAULT 'PENDENTE';

ALTER TABLE `cbp_parcelas_acordo` 
ADD COLUMN IF NOT EXISTS `honorario_id` int(11) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `honorario_status` enum('PENDENTE','PAGO') DEFAULT 'PENDENTE';

-- 4. Criar procedimento para reprocessar honorários existentes
DROP PROCEDURE IF EXISTS `reprocessar_honorarios`;

DELIMITER $$
CREATE PROCEDURE `reprocessar_honorarios`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE alvara_id, processo_id, associado_id, advogado_id INT;
    DECLARE data_recebimento DATE;
    DECLARE valor DECIMAL(10,2);
    DECLARE porcentagem DECIMAL(10,4);
    DECLARE valor_honorario DECIMAL(10,2);
    
    -- Query corrigida para Alvará - usando JOIN adequado para obter associado_id
    DECLARE alvara_cursor CURSOR FOR 
        SELECT 
            a.id, 
            a.processo_id, 
            p.associado_id,  -- Verificar se o campo existe
            p.advogado_id,   -- Verificar se o campo existe
            a.data_recebimento, 
            a.valor
        FROM 
            cbp_alvaras a
            INNER JOIN cbp_processos_judiciais p ON a.processo_id = p.id
        WHERE 
            a.honorario_id IS NULL
            AND NOT EXISTS (SELECT 1 FROM cbp_honorarios h WHERE h.alvara_id = a.id);
            
    -- Query corrigida para parcelas - usando JOIN adequado
    DECLARE parcela_cursor CURSOR FOR 
        SELECT 
            pa.id, 
            a.processo_id, 
            p.associado_id,  -- Verificar se o campo existe
            p.advogado_id,   -- Verificar se o campo existe
            pa.data_pagamento, 
            pa.valor_pago
        FROM 
            cbp_parcelas_acordo pa
            INNER JOIN cbp_acordos a ON pa.acordo_id = a.id
            INNER JOIN cbp_processos_judiciais p ON a.processo_id = p.id
        WHERE 
            pa.status = 'PAGO'
            AND pa.honorario_id IS NULL
            AND NOT EXISTS (SELECT 1 FROM cbp_honorarios h WHERE h.parcela_id = pa.id);
            
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- Buscar a porcentagem padrão de honorários
    SELECT valor INTO porcentagem 
    FROM cbp_configuracoes 
    WHERE chave = 'porcentagem_padrao_honorarios';
    
    IF porcentagem IS NULL THEN
        SET porcentagem = 13.0435; -- Valor padrão caso não exista configuração
    END IF;
    
    -- Processar alvarás sem honorários
    OPEN alvara_cursor;
    
    read_loop_alvaras: LOOP
        FETCH alvara_cursor INTO alvara_id, processo_id, associado_id, advogado_id, data_recebimento, valor;
        
        IF done THEN
            LEAVE read_loop_alvaras;
        END IF;
        
        -- Calcular valor do honorário
        SET valor_honorario = valor * (porcentagem / 100);
        
        -- Inserir honorário para o alvará
        INSERT INTO cbp_honorarios (
            processo_id, associado_id, advogado_id, alvara_id, tipo,
            valor_recebido, porcentagem_honorario, valor_honorario,
            status, data_recebimento, created_at, updated_at
        ) VALUES (
            processo_id, associado_id, advogado_id, alvara_id, 'ALVARA',
            valor, porcentagem, valor_honorario,
            'PENDENTE', data_recebimento, NOW(), NOW()
        );
        
        -- Atualizar o alvará com o ID do honorário
        UPDATE cbp_alvaras 
        SET honorario_id = LAST_INSERT_ID() 
        WHERE id = alvara_id;
    END LOOP;
    
    CLOSE alvara_cursor;
    
    -- Resetar o flag done
    SET done = FALSE;
    
    -- Processar parcelas pagas sem honorários
    OPEN parcela_cursor;
    
    read_loop_parcelas: LOOP
        FETCH parcela_cursor INTO alvara_id, processo_id, associado_id, advogado_id, data_recebimento, valor;
        
        IF done THEN
            LEAVE read_loop_parcelas;
        END IF;
        
        -- Calcular valor do honorário
        SET valor_honorario = valor * (porcentagem / 100);
        
        -- Inserir honorário para a parcela
        INSERT INTO cbp_honorarios (
            processo_id, associado_id, advogado_id, parcela_id, tipo,
            valor_recebido, porcentagem_honorario, valor_honorario,
            status, data_recebimento, created_at, updated_at
        ) VALUES (
            processo_id, associado_id, advogado_id, alvara_id, 'PARCELA',
            valor, porcentagem, valor_honorario,
            'PENDENTE', data_recebimento, NOW(), NOW()
        );
        
        -- Atualizar a parcela com o ID do honorário
        UPDATE cbp_parcelas_acordo 
        SET honorario_id = LAST_INSERT_ID() 
        WHERE id = alvara_id;
    END LOOP;
    
    CLOSE parcela_cursor;
END$$
DELIMITER ;

-- 5. Executar o procedimento de reprocessamento
-- Comentado para executar manualmente após verificação
-- CALL reprocessar_honorarios();

-- 6. Adicionar índices para melhorar a performance
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_tipo` (`tipo`);
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_status` (`status`);
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_processo` (`processo_id`);
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_associado` (`associado_id`);
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_advogado` (`advogado_id`);
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_data` (`data_recebimento`); 
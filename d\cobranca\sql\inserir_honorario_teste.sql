-- Script para inserir um honorário de teste
-- Execute este script apenas para verificar se a exibição está funcionando corretamente

-- 1. Primeiro vamos verificar se existe algum processo, associado e advogado
SET @processo_id = (SELECT id FROM cbp_processos_judiciais ORDER BY id LIMIT 1);
SET @associado_id = (SELECT associado_id FROM cbp_processos_judiciais WHERE id = @processo_id);
SET @advogado_id = (SELECT advogado_id FROM cbp_processos_judiciais WHERE id = @processo_id);

-- Exibir valores para verificação
SELECT 'Verificando IDs existentes' as mensagem;
SELECT @processo_id as processo_id, @associado_id as associado_id, @advogado_id as advogado_id;

-- 2. Inserir um honorário de teste manualmente
INSERT INTO cbp_honorarios (
    processo_id,
    associado_id,
    advogado_id,
    tipo,
    valor_recebido,
    porcentagem_honorario,
    valor_honorario,
    status,
    data_recebimento,
    created_at,
    updated_at
) VALUES (
    @processo_id,  -- processo_id
    @associado_id, -- associado_id
    @advogado_id,  -- advogado_id
    'TESTE',       -- tipo
    1000.00,       -- valor_recebido
    13.0435,       -- porcentagem_honorario
    130.435,       -- valor_honorario (13.0435% de 1000)
    'PENDENTE',    -- status
    CURDATE(),     -- data_recebimento
    NOW(),         -- created_at
    NOW()          -- updated_at
);

-- 3. Verificar se o honorário foi inserido
SELECT 'Verificando honorário inserido' as mensagem;
SELECT * FROM cbp_honorarios ORDER BY id DESC LIMIT 1; 
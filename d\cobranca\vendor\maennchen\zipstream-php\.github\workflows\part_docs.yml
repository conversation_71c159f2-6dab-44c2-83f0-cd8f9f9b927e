on:
  workflow_call: {}

name: "Documentation"

permissions:
  contents: read

jobs:
  generate:
    name: "Generate"

    runs-on: ubuntu-latest

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@cb605e52c26070c328afc4562f0b4ada7618a84e # v2.10.4
        with:
          egress-policy: audit

      - name: Checkout Code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: SetUp PHP
        id: setup-php
        uses: shivammathur/setup-php@9e72090525849c5e82e596468b86eb55e9cc5401 # v2
        with:
          php-version: "8.3"
          tools: phive
      - name: <PERSON><PERSON>
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        id: cache
        with:
          path: ~/.phive
          key: tools-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-${{ hashFiles('**/phars.xml') }}
          restore-keys: |
            tools-${{ runner.os }}-${{ steps.setup-php.outputs.php-version }}-
            tools-${{ steps.setup-php.outputs.php-version }}-
            tools-
      - name: Install Tools
        run: composer run install:tools
      - name: Generate Docs
        run: composer run docs:generate
      - uses: actions/upload-artifact@65c4c4a1ddee5b72f698fdd19549f0f0fb45cf08 # v4.6.0
        with:
          name: docs
          path: docs
      - name: Package for GitHub Pages
        uses: actions/upload-pages-artifact@56afc609e74202658d3ffba0e8f6dda462b719fa # v3.0.1
        with:
          path: docs

